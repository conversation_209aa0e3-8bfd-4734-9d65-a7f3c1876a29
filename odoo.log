Exception in thread odoo.service.cron.cron1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
Exception in thread odoo.service.cron.cron0:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
38914 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:40:21,242 38914 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:40:21,359 38914 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:40:21,394 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:40:21,399 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:40:21,408 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:40:21,451 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-31 10:40:21,471 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:40:21,474 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.119s 
2025-07-31 10:40:28,015 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:40:28,070 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:40:28] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 8 0.014 0.051
2025-07-31 10:40:28,391 38914 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:42:38,226 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web?reload=true HTTP/1.1" 200 - 69 0.040 0.521
2025-07-31 10:42:38,515 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.003
2025-07-31 10:42:38,768 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:42:38,768 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:42:38,770 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:42:38,771 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:42:38,772 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:42:38,773 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:42:38,774 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:42:38,776 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:42:38,777 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:42:38,783 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.008 0.226
2025-07-31 10:42:38,787 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.223
2025-07-31 10:42:38,797 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/action/load HTTP/1.1" 200 - 10 0.008 0.280
2025-07-31 10:42:38,815 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.019 0.004
2025-07-31 10:42:38,843 39742 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:42:38,844 39742 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:42:38,844 39742 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:42:38,844 39742 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:42:38,849 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 47 0.025 0.017
2025-07-31 10:42:38,873 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.017
2025-07-31 10:42:38,893 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 39 0.015 0.022
2025-07-31 10:42:38,898 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.036
2025-07-31 10:42:38,913 39742 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:42:39,125 39742 INFO ? odoo.service.server: Initiating shutdown 
2025-07-31 10:42:39,125 39742 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-31 10:42:39,162 39742 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:42:39,165 39742 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:42:39,169 39742 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:42:39,214 39742 INFO odoo odoo.modules.loading: 11 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-31 10:42:39,234 39742 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:42:39,236 39742 INFO odoo odoo.modules.registry: Registry loaded in 0.111s 
2025-07-31 10:42:40,856 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:40] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.008
2025-07-31 10:42:43,859 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:43] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.022 0.037
2025-07-31 10:42:43,879 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:43] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.011
2025-07-31 10:42:45,603 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.016 0.027
2025-07-31 10:42:45,652 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.039
2025-07-31 10:42:45,823 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.023 0.019
2025-07-31 10:42:45,835 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.043
2025-07-31 10:43:04,118 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.009 0.039
2025-07-31 10:43:04,186 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /hr_work_entry_contract/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 10:43:04,191 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /website_crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,191 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /hr_timesheet/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-07-31 10:43:04,192 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /account_check_printing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,195 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /google_calendar/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,196 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /event/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-31 10:43:04,199 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /google_gmail/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 10:43:04,841 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.008 0.043
2025-07-31 10:43:09,388 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:09] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.026 0.034
2025-07-31 10:43:09,408 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:09] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.011
2025-07-31 10:43:13,922 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:13,923 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:13,924 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:14,355 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:14,359 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:14,370 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:14,373 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:14,785 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:14,788 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:14,789 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:14,789 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:14,807 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:14,869 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:14,896 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:14,907 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:14,913 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:14,931 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:14,932 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:14,946 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.16s, 291 queries (+291 other) 
2025-07-31 10:43:14,946 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:15,126 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:15,378 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:15,379 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:15,381 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:15,383 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:15,438 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:15,439 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:43:30,558 38914 ERROR odoo odoo.sql_db: bad query: ALTER TABLE "res_users" ADD COLUMN "notification_type" VARCHAR ; COMMENT ON COLUMN "res_users"."notification_type" IS 'Notification'
ERROR: canceling statement due to lock timeout
 
2025-07-31 10:43:30,568 38914 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:43:30,569 38914 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 207, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 569, in init_models
    model._auto_init()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/models.py", line 3182, in _auto_init
    new = field.update_db(self, columns)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1005, in update_db
    self.update_db_column(model, column)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1035, in update_db_column
    sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/sql.py", line 293, in create_column
    cr.execute(sql)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/sql_db.py", line 335, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: canceling statement due to lock timeout

2025-07-31 10:43:30,583 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:30,592 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:30,594 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:30,602 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:30,625 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:43:30,627 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.049s 
2025-07-31 10:43:30,627 38914 INFO odoo odoo.service.model: LOCK_NOT_AVAILABLE, 4 tries left, try again in 0.8990 sec... 
2025-07-31 10:43:31,532 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:43:31,550 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:31,550 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:31,552 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:31,638 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:31,644 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:31,659 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:31,660 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:32,261 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:32,265 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:32,266 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:32,266 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:32,279 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:32,291 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:32,343 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:32,346 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:32,350 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:32,367 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:32,367 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:32,379 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.11s, 262 queries (+262 other) 
2025-07-31 10:43:32,379 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:32,401 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:32,528 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:32,552 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:32,552 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:43:47,664 38914 ERROR odoo odoo.sql_db: bad query: ALTER TABLE "res_users" ADD COLUMN "notification_type" VARCHAR ; COMMENT ON COLUMN "res_users"."notification_type" IS 'Notification'
ERROR: canceling statement due to lock timeout
 
2025-07-31 10:43:47,669 38914 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:43:47,670 38914 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 207, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 569, in init_models
    model._auto_init()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/models.py", line 3182, in _auto_init
    new = field.update_db(self, columns)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1005, in update_db
    self.update_db_column(model, column)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1035, in update_db_column
    sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/sql.py", line 293, in create_column
    cr.execute(sql)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/sql_db.py", line 335, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: canceling statement due to lock timeout

2025-07-31 10:43:47,675 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:47,682 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:47,686 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:47,692 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:47,714 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:43:47,716 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.046s 
2025-07-31 10:43:47,716 38914 INFO odoo odoo.service.model: LOCK_NOT_AVAILABLE, 3 tries left, try again in 1.8033 sec... 
2025-07-31 10:43:49,528 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:43:49,579 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:49,579 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:49,583 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:49,689 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:49,694 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:49,707 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:49,708 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:50,916 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:50,929 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:50,932 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:50,932 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:50,963 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:50,997 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:51,111 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:51,119 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:51,135 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:51,191 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:51,192 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:51,244 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.31s, 262 queries (+262 other) 
2025-07-31 10:43:51,244 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:51,292 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:51,554 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:51,555 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:44:06,551 38914 INFO odoo odoo.service.server: Initiating server reload 
2025-07-31 10:44:06,552 39742 WARNING odoo odoo.cli.shell: Could not start 'python' shell. 
2025-07-31 10:44:06,562 38914 INFO odoo odoo.models: Prepare computation of res.users.notification_type 
2025-07-31 10:44:06,647 38914 INFO odoo odoo.models: Prepare computation of res.partner.email_normalized 
2025-07-31 10:44:07,395 38914 INFO odoo odoo.modules.loading: loading mail/data/mail_groups.xml 
2025-07-31 10:44:07,442 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_activity_schedule_views.xml 
2025-07-31 10:44:07,446 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_blacklist_remove_views.xml 
2025-07-31 10:44:07,449 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_compose_message_views.xml 
2025-07-31 10:44:07,457 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_resend_message_views.xml 
2025-07-31 10:44:07,462 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_resend_partner_views.xml 
2025-07-31 10:44:07,466 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_template_preview_views.xml 
2025-07-31 10:44:07,470 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_wizard_invite_views.xml 
2025-07-31 10:44:07,472 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_template_reset_views.xml 
2025-07-31 10:44:07,476 38914 INFO odoo odoo.modules.loading: loading mail/views/fetchmail_views.xml 
2025-07-31 10:44:07,484 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_subtype_views.xml 
2025-07-31 10:44:07,488 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_tracking_value_views.xml 
2025-07-31 10:44:07,493 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_notification_views.xml 
2025-07-31 10:44:07,497 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_views.xml 
2025-07-31 10:44:07,510 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_schedule_views.xml 
2025-07-31 10:44:07,516 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_mail_views.xml 
2025-07-31 10:44:07,526 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_followers_views.xml 
2025-07-31 10:44:07,530 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_ice_server_views.xml 
2025-07-31 10:44:07,535 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_member_views.xml 
2025-07-31 10:44:07,539 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_rtc_session_views.xml 
2025-07-31 10:44:07,544 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_link_preview_views.xml 
2025-07-31 10:44:07,549 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss/discuss_gif_favorite_views.xml 
2025-07-31 10:44:07,553 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_views.xml 
2025-07-31 10:44:07,562 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_shortcode_views.xml 
2025-07-31 10:44:07,568 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_views.xml 
2025-07-31 10:44:07,604 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_plan_views.xml 
2025-07-31 10:44:07,615 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_plan_template_views.xml 
2025-07-31 10:44:07,619 38914 INFO odoo odoo.modules.loading: loading mail/views/res_config_settings_views.xml 
2025-07-31 10:44:07,629 38914 INFO odoo odoo.modules.loading: loading mail/data/ir_config_parameter_data.xml 
2025-07-31 10:44:07,632 38914 INFO odoo odoo.modules.loading: loading mail/data/res_partner_data.xml 
2025-07-31 10:44:07,661 38914 INFO odoo odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 6 connections  
2025-07-31 10:44:08,230 38914 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:44:08,230 38914 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:44:08,230 38914 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:44:08,230 38914 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:44:08,319 38914 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:44:08,432 38914 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:44:08,453 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:44:08,457 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:44:08,462 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:44:08,492 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.03s, 0 queries (+0 extra) 
2025-07-31 10:44:08,509 38914 ERROR odoo odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:44:08,509 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:44:08,511 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.082s 
2025-07-31 10:44:10,313 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:44:10,341 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:44:10] "GET /web/service-worker.js HTTP/1.1" 200 - 8 0.012 0.018
2025-07-31 10:49:46,986 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:46] "GET /web?reload=true HTTP/1.1" 200 - 70 0.068 0.543
2025-07-31 10:49:47,287 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.006
2025-07-31 10:49:47,311 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.010 0.009
2025-07-31 10:49:47,534 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.192
2025-07-31 10:49:47,542 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.007 0.194
2025-07-31 10:49:47,564 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:49:47,564 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:49:47,566 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:49:47,566 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:49:47,567 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:49:47,569 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:49:47,569 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:49:47,571 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:49:47,571 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:49:47,593 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.307
2025-07-31 10:49:47,641 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.023 0.017
2025-07-31 10:49:47,661 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.013
2025-07-31 10:49:47,697 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.038
2025-07-31 10:49:47,703 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.035 0.020
2025-07-31 10:49:47,780 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /account_ledger/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-07-31 10:49:47,783 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /jgq_reportbro/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-07-31 10:49:47,784 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /spiffy_theme_backend/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-07-31 10:49:47,814 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /expense_reimburse/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-07-31 10:49:47,815 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /izi_dashboard/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.009
2025-07-31 10:49:47,816 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /business_trip/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.020
2025-07-31 10:49:47,817 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /mommy_base/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /sale_purchase_advanced_with_contact/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /purchase_request/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /mommy_payment_alipay/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.011
2025-07-31 10:49:47,852 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web_approval/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:49:47,855 38914 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:49:49,625 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:49] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.009
2025-07-31 10:52:11,709 44575 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:52:11,709 44575 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:52:11,709 44575 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:52:11,709 44575 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:52:11,982 44575 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:52:12,237 44575 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:52:46,154 44575 INFO ? odoo.service.server: Initiating shutdown 
2025-07-31 10:52:46,156 44575 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-31 10:52:46,316 44575 INFO ? odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 2 connections  
2025-07-31 10:52:52,176 44728 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:52:52,176 44728 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:52:52,176 44728 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:52:52,176 44728 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:52:52,238 44728 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:52:52,359 44728 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:53:39,151 44728 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-31 10:53:39,159 44728 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:53:39,173 44728 INFO ? odoo.modules.loading: loading 11 modules... 
2025-07-31 10:53:39,436 44728 INFO ? odoo.modules.loading: 11 modules loaded in 0.26s, 0 queries (+0 extra) 
2025-07-31 10:53:39,462 44728 ERROR ? odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:53:39,462 44728 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-31 10:53:39,467 44728 INFO ? odoo.modules.registry: Registry loaded in 0.357s 
2025-07-31 10:53:39,468 44728 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:53:39,481 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:39] "GET / HTTP/1.1" 303 - 21 0.032 0.342
2025-07-31 10:53:39,956 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:39] "GET /web HTTP/1.1" 200 - 71 0.073 0.395
2025-07-31 10:53:40,498 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.006
2025-07-31 10:53:40,730 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.009 0.014
2025-07-31 10:53:40,749 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.003
2025-07-31 10:53:40,764 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.003
2025-07-31 10:53:40,972 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:53:40,973 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:53:40,974 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:53:40,975 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:53:40,975 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:53:40,977 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:53:40,977 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:53:40,978 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:53:40,978 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:53:41,002 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.282
2025-07-31 10:53:41,072 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.038 0.025
2025-07-31 10:53:41,075 44728 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:53:41,119 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.023
2025-07-31 10:53:41,158 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.053
2025-07-31 10:53:41,158 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.033 0.031
2025-07-31 10:53:43,008 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:43] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.011
2025-07-31 10:53:46,593 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/action_get HTTP/1.1" 200 - 9 0.004 0.009
2025-07-31 10:53:46,686 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 25 0.022 0.064
2025-07-31 10:53:46,743 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 11 0.008 0.040
2025-07-31 10:53:46,800 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "GET /web/bundle/web_editor.backend_assets_wysiwyg?lang=zh_CN HTTP/1.1" 200 - 7 0.002 0.026
2025-07-31 10:53:46,860 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 8 0.005 0.021
2025-07-31 10:53:46,926 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "GET /web/image?model=res.users&id=2&field=avatar_128&unique=1753959226757 HTTP/1.1" 200 - 9 0.006 0.017
2025-07-31 10:53:50,717 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/action/load HTTP/1.1" 200 - 8 0.007 0.017
2025-07-31 10:53:50,774 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 7 0.010 0.029
2025-07-31 10:53:50,856 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 26 0.026 0.042
2025-07-31 10:53:50,896 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.002 0.007
2025-07-31 10:53:50,915 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /base_setup/data HTTP/1.1" 200 - 6 0.005 0.017
2025-07-31 10:53:54,660 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:54] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 8 0.008 0.024
2025-07-31 10:54:03,652 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.008
2025-07-31 10:54:03,692 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.009 0.040
2025-07-31 10:54:03,704 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.042 0.019
2025-07-31 10:54:06,972 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:06] "POST /web/dataset/call_kw/res.users/action_get HTTP/1.1" 200 - 8 0.003 0.012
2025-07-31 10:54:07,025 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:07] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 11 0.006 0.037
2025-07-31 10:54:07,079 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:07] "GET /web/image?model=res.users&id=2&field=avatar_128&unique=1753959247029 HTTP/1.1" 200 - 9 0.004 0.016
2025-07-31 10:54:19,073 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.037 0.058
2025-07-31 10:54:19,103 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:19] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.016
2025-07-31 10:54:21,328 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:21,329 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:54:21,330 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:21,381 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:54:21,558 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:54:21,558 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:21] "POST /web/dataset/call_button HTTP/1.1" 200 - 12 0.013 0.230
2025-07-31 10:54:33,941 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:33,941 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:54:33,942 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:33,982 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:54:33,985 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:54:33,985 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:33] "POST /web/dataset/call_button HTTP/1.1" 200 - 11 0.013 0.055
2025-07-31 10:55:43,975 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:43] "GET /web HTTP/1.1" 200 - 9 0.009 0.032
2025-07-31 10:55:44,425 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.006 0.007
2025-07-31 10:55:44,429 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/action/load HTTP/1.1" 200 - 9 0.009 0.010
2025-07-31 10:55:44,442 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.013
2025-07-31 10:55:44,486 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.017
2025-07-31 10:55:44,486 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.001 0.017
2025-07-31 10:55:44,494 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 5 0.016 0.009
2025-07-31 10:55:44,496 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.004
2025-07-31 10:55:44,527 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.020 0.016
2025-07-31 10:55:44,539 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.041
2025-07-31 10:55:46,525 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:46] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.008
2025-07-31 10:55:47,517 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.046 0.071
2025-07-31 10:55:47,552 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.018
2025-07-31 10:55:48,644 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:55:48,645 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:55:48,647 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:55:48,690 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:55:48,693 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:55:48,694 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:48] "POST /web/dataset/call_button HTTP/1.1" 200 - 11 0.010 0.051
2025-07-31 10:56:37,851 45618 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:56:37,851 45618 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:56:37,851 45618 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:56:37,851 45618 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:56:37,915 45618 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:56:38,024 45618 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:56:43,411 45618 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:43,424 45618 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:56:43,435 45618 INFO ? odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:43,491 45618 INFO ? odoo.modules.loading: 11 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-07-31 10:56:43,512 45618 ERROR ? odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:56:43,512 45618 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-31 10:56:43,515 45618 INFO ? odoo.modules.registry: Registry loaded in 0.151s 
2025-07-31 10:56:43,516 45618 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:56:43,554 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:43] "GET / HTTP/1.1" 303 - 21 0.054 0.141
2025-07-31 10:56:44,107 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web HTTP/1.1" 200 - 71 0.066 0.480
2025-07-31 10:56:44,619 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.009
2025-07-31 10:56:44,838 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.010 0.014
2025-07-31 10:56:44,860 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.004
2025-07-31 10:56:44,875 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.003
2025-07-31 10:56:45,109 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:56:45,110 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:56:45,112 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:56:45,113 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:56:45,113 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:56:45,115 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:56:45,116 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:56:45,116 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:56:45,118 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:56:45,134 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.312
2025-07-31 10:56:45,182 45618 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:56:45,191 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.027 0.023
2025-07-31 10:56:45,220 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.022
2025-07-31 10:56:45,249 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.021 0.031
2025-07-31 10:56:45,263 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.052
2025-07-31 10:56:47,093 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:47] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.005
2025-07-31 10:56:49,046 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:49] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.044 0.049
2025-07-31 10:56:49,068 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:49] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.008
2025-07-31 10:56:50,198 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:56:50,199 45618 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:56:50,200 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:56:50,520 45618 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:50,524 45618 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,534 45618 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:56:50,535 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:56:50,866 45618 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:50,870 45618 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,871 45618 INFO odoo odoo.modules.loading: loading 27 modules... 
2025-07-31 10:56:50,871 45618 INFO odoo odoo.modules.loading: Loading module izi_data (2/27) 
2025-07-31 10:56:50,881 45618 WARNING odoo py.warnings: /Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/controllers/controllers.py:76: SyntaxWarning: "is" with a literal. Did you mean "=="?
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 973, in _bootstrap
    self._bootstrap_inner()
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 953, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/server.py", line 127, in __init__
    super().__init__(*args, **kwargs)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 747, in __init__
    self.handle()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 342, in handle
    BaseHTTPRequestHandler.handle(self)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/http/server.py", line 433, in handle
    self.handle_one_request()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 374, in handle_one_request
    self.run_wsgi()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 319, in run_wsgi
    execute(self.server.app)
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 308, in execute
    application_iter = app(environ, start_response)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2375, in __call__
    response = request._serve_db()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1950, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/model.py", line 152, in retrying
    result = func()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1978, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2182, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 786, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 29, in call_button
    action = self._call_kw(model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 21, in _call_kw
    return call_kw(Model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 484, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 469, in _call_kw_multi
    result = method(recs, *args, **kwargs)
  File "<decorator-gen-77>", line 2, in button_immediate_install
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 469, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 593, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "<decorator-gen-16>", line 2, in new
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/controllers/__init__.py", line 5, in <module>
    from . import controllers
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/warnings.py", line 109, in _showwarnmsg
    sw(msg.message, msg.category, msg.filename, msg.lineno,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/netsvc.py", line 321, in showwarning_with_traceback
    for frame in traceback.extract_stack():
 
2025-07-31 10:56:50,883 45618 CRITICAL odoo odoo.modules.module: Couldn't load module izi_data 
2025-07-31 10:56:50,885 45618 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:56:50,885 45618 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/__init__.py", line 4, in <module>
    from . import common, wizard
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/__init__.py", line 4, in <module>
    from . import izi_tools
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/izi_tools.py", line 12, in <module>
    import pandas
ModuleNotFoundError: No module named 'pandas'
2025-07-31 10:56:50,887 45618 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:50,890 45618 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,891 45618 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:50,894 45618 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,905 45618 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:56:50,906 45618 INFO odoo odoo.modules.registry: Registry loaded in 0.021s 
2025-07-31 10:56:50,907 45618 ERROR odoo odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2375, in __call__
    response = request._serve_db()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1950, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/model.py", line 152, in retrying
    result = func()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1978, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2182, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 786, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 29, in call_button
    action = self._call_kw(model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 21, in _call_kw
    return call_kw(Model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 484, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 469, in _call_kw_multi
    result = method(recs, *args, **kwargs)
  File "<decorator-gen-77>", line 2, in button_immediate_install
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 469, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 593, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "<decorator-gen-16>", line 2, in new
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/__init__.py", line 4, in <module>
    from . import common, wizard
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/__init__.py", line 4, in <module>
    from . import izi_tools
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/izi_tools.py", line 12, in <module>
    import pandas
ModuleNotFoundError: No module named 'pandas'
2025-07-31 10:56:50,907 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:50] "POST /web/dataset/call_button HTTP/1.1" 200 - 1521 0.268 0.452

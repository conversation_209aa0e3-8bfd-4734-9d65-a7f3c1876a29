Exception in thread odoo.service.cron.cron1:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
Exception in thread odoo.service.cron.cron0:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
38914 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:40:21,242 38914 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:40:21,359 38914 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:40:21,394 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:40:21,399 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:40:21,408 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:40:21,451 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-31 10:40:21,471 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:40:21,474 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.119s 
2025-07-31 10:40:28,015 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:40:28,070 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:40:28] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 8 0.014 0.051
2025-07-31 10:40:28,391 38914 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:42:38,226 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web?reload=true HTTP/1.1" 200 - 69 0.040 0.521
2025-07-31 10:42:38,515 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.003
2025-07-31 10:42:38,768 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:42:38,768 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:42:38,770 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:42:38,771 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:42:38,772 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:42:38,773 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:42:38,774 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:42:38,776 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:42:38,777 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:42:38,783 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.008 0.226
2025-07-31 10:42:38,787 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.223
2025-07-31 10:42:38,797 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/action/load HTTP/1.1" 200 - 10 0.008 0.280
2025-07-31 10:42:38,815 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.019 0.004
2025-07-31 10:42:38,843 39742 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:42:38,844 39742 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:42:38,844 39742 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:42:38,844 39742 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:42:38,849 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 47 0.025 0.017
2025-07-31 10:42:38,873 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.017
2025-07-31 10:42:38,893 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 39 0.015 0.022
2025-07-31 10:42:38,898 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:38] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.036
2025-07-31 10:42:38,913 39742 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:42:39,125 39742 INFO ? odoo.service.server: Initiating shutdown 
2025-07-31 10:42:39,125 39742 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-31 10:42:39,162 39742 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:42:39,165 39742 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:42:39,169 39742 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:42:39,214 39742 INFO odoo odoo.modules.loading: 11 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-31 10:42:39,234 39742 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:42:39,236 39742 INFO odoo odoo.modules.registry: Registry loaded in 0.111s 
2025-07-31 10:42:40,856 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:40] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.008
2025-07-31 10:42:43,859 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:43] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.022 0.037
2025-07-31 10:42:43,879 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:43] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.011
2025-07-31 10:42:45,603 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.016 0.027
2025-07-31 10:42:45,652 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.039
2025-07-31 10:42:45,823 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.023 0.019
2025-07-31 10:42:45,835 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:42:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.043
2025-07-31 10:43:04,118 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.009 0.039
2025-07-31 10:43:04,186 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /hr_work_entry_contract/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 10:43:04,191 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /website_crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,191 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /hr_timesheet/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-07-31 10:43:04,192 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /account_check_printing/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,195 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /google_calendar/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:43:04,196 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /event/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-31 10:43:04,199 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "GET /google_gmail/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 10:43:04,841 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:04] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.008 0.043
2025-07-31 10:43:09,388 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:09] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 36 0.026 0.034
2025-07-31 10:43:09,408 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:43:09] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.011
2025-07-31 10:43:13,922 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:13,923 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:13,924 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:14,355 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:14,359 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:14,370 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:14,373 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:14,785 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:14,788 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:14,789 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:14,789 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:14,807 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:14,869 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:14,896 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:14,907 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:14,913 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:14,931 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:14,932 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:14,946 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.16s, 291 queries (+291 other) 
2025-07-31 10:43:14,946 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:15,126 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:15,378 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:15,379 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:15,381 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:15,382 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:15,383 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:15,438 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:15,439 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:43:30,558 38914 ERROR odoo odoo.sql_db: bad query: ALTER TABLE "res_users" ADD COLUMN "notification_type" VARCHAR ; COMMENT ON COLUMN "res_users"."notification_type" IS 'Notification'
ERROR: canceling statement due to lock timeout
 
2025-07-31 10:43:30,568 38914 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:43:30,569 38914 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 207, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 569, in init_models
    model._auto_init()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/models.py", line 3182, in _auto_init
    new = field.update_db(self, columns)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1005, in update_db
    self.update_db_column(model, column)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1035, in update_db_column
    sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/sql.py", line 293, in create_column
    cr.execute(sql)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/sql_db.py", line 335, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: canceling statement due to lock timeout

2025-07-31 10:43:30,583 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:30,592 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:30,594 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:30,602 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:30,625 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:43:30,627 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.049s 
2025-07-31 10:43:30,627 38914 INFO odoo odoo.service.model: LOCK_NOT_AVAILABLE, 4 tries left, try again in 0.8990 sec... 
2025-07-31 10:43:31,532 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:43:31,550 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:31,550 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:31,552 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:31,638 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:31,644 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:31,659 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:31,660 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:32,261 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:32,265 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:43:32,266 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:32,266 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:32,279 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:32,291 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:32,343 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:32,346 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:32,350 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:32,367 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:32,367 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:32,379 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.11s, 262 queries (+262 other) 
2025-07-31 10:43:32,379 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:32,401 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:32,528 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:32,529 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:32,552 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:32,552 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:43:47,664 38914 ERROR odoo odoo.sql_db: bad query: ALTER TABLE "res_users" ADD COLUMN "notification_type" VARCHAR ; COMMENT ON COLUMN "res_users"."notification_type" IS 'Notification'
ERROR: canceling statement due to lock timeout
 
2025-07-31 10:43:47,669 38914 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:43:47,670 38914 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 207, in load_module_graph
    registry.init_models(env.cr, model_names, {'module': package.name}, new_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 569, in init_models
    model._auto_init()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/models.py", line 3182, in _auto_init
    new = field.update_db(self, columns)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1005, in update_db
    self.update_db_column(model, column)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/fields.py", line 1035, in update_db_column
    sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/sql.py", line 293, in create_column
    cr.execute(sql)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/sql_db.py", line 335, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: canceling statement due to lock timeout

2025-07-31 10:43:47,675 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:47,682 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:47,686 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:47,692 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:47,714 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:43:47,716 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.046s 
2025-07-31 10:43:47,716 38914 INFO odoo odoo.service.model: LOCK_NOT_AVAILABLE, 3 tries left, try again in 1.8033 sec... 
2025-07-31 10:43:49,528 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:43:49,579 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:49,579 38914 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:43:49,583 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['分析会计'] to user admin #2 via 127.0.0.1 
2025-07-31 10:43:49,689 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:43:49,694 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:49,707 38914 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:43:49,708 38914 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:43:50,916 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:43:50,929 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:43:50,932 38914 INFO odoo odoo.modules.loading: loading 25 modules... 
2025-07-31 10:43:50,932 38914 INFO odoo odoo.modules.loading: Loading module uom (2/25) 
2025-07-31 10:43:50,963 38914 INFO odoo odoo.modules.registry: module uom: creating or updating database tables 
2025-07-31 10:43:50,997 38914 INFO odoo odoo.modules.loading: loading uom/data/uom_data.xml 
2025-07-31 10:43:51,111 38914 INFO odoo odoo.modules.loading: loading uom/security/uom_security.xml 
2025-07-31 10:43:51,119 38914 INFO odoo odoo.modules.loading: loading uom/security/ir.model.access.csv 
2025-07-31 10:43:51,135 38914 INFO odoo odoo.modules.loading: loading uom/views/uom_uom_views.xml 
2025-07-31 10:43:51,191 38914 INFO odoo odoo.addons.base.models.ir_module: module uom: loading translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:51,192 38914 INFO odoo odoo.tools.translate: loading base translation file /Users/<USER>/MelGeek/odoo-17.0/addons/uom/i18n/zh_CN.po for language zh_CN 
2025-07-31 10:43:51,244 38914 INFO odoo odoo.modules.loading: Module uom loaded in 0.31s, 262 queries (+262 other) 
2025-07-31 10:43:51,244 38914 INFO odoo odoo.modules.loading: Loading module mail (11/25) 
2025-07-31 10:43:51,292 38914 INFO odoo odoo.modules.registry: module mail: creating or updating database tables 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.partner_ids 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.template_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_type_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_id 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_method 
2025-07-31 10:43:51,501 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_summary 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_date_deadline_range_type 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_type 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_user_field_name 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.activity_note 
2025-07-31 10:43:51,502 38914 INFO odoo odoo.models: Prepare computation of ir.actions.server.mail_post_autofollow 
2025-07-31 10:43:51,554 38914 INFO odoo odoo.models: Prepare computation of res.company.email_primary_color 
2025-07-31 10:43:51,555 38914 INFO odoo odoo.models: Prepare computation of res.company.email_secondary_color 
2025-07-31 10:44:06,551 38914 INFO odoo odoo.service.server: Initiating server reload 
2025-07-31 10:44:06,552 39742 WARNING odoo odoo.cli.shell: Could not start 'python' shell. 
2025-07-31 10:44:06,562 38914 INFO odoo odoo.models: Prepare computation of res.users.notification_type 
2025-07-31 10:44:06,647 38914 INFO odoo odoo.models: Prepare computation of res.partner.email_normalized 
2025-07-31 10:44:07,395 38914 INFO odoo odoo.modules.loading: loading mail/data/mail_groups.xml 
2025-07-31 10:44:07,442 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_activity_schedule_views.xml 
2025-07-31 10:44:07,446 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_blacklist_remove_views.xml 
2025-07-31 10:44:07,449 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_compose_message_views.xml 
2025-07-31 10:44:07,457 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_resend_message_views.xml 
2025-07-31 10:44:07,462 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_resend_partner_views.xml 
2025-07-31 10:44:07,466 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_template_preview_views.xml 
2025-07-31 10:44:07,470 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_wizard_invite_views.xml 
2025-07-31 10:44:07,472 38914 INFO odoo odoo.modules.loading: loading mail/wizard/mail_template_reset_views.xml 
2025-07-31 10:44:07,476 38914 INFO odoo odoo.modules.loading: loading mail/views/fetchmail_views.xml 
2025-07-31 10:44:07,484 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_subtype_views.xml 
2025-07-31 10:44:07,488 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_tracking_value_views.xml 
2025-07-31 10:44:07,493 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_notification_views.xml 
2025-07-31 10:44:07,497 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_views.xml 
2025-07-31 10:44:07,510 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_message_schedule_views.xml 
2025-07-31 10:44:07,516 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_mail_views.xml 
2025-07-31 10:44:07,526 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_followers_views.xml 
2025-07-31 10:44:07,530 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_ice_server_views.xml 
2025-07-31 10:44:07,535 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_member_views.xml 
2025-07-31 10:44:07,539 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_rtc_session_views.xml 
2025-07-31 10:44:07,544 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_link_preview_views.xml 
2025-07-31 10:44:07,549 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss/discuss_gif_favorite_views.xml 
2025-07-31 10:44:07,553 38914 INFO odoo odoo.modules.loading: loading mail/views/discuss_channel_views.xml 
2025-07-31 10:44:07,562 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_shortcode_views.xml 
2025-07-31 10:44:07,568 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_views.xml 
2025-07-31 10:44:07,604 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_plan_views.xml 
2025-07-31 10:44:07,615 38914 INFO odoo odoo.modules.loading: loading mail/views/mail_activity_plan_template_views.xml 
2025-07-31 10:44:07,619 38914 INFO odoo odoo.modules.loading: loading mail/views/res_config_settings_views.xml 
2025-07-31 10:44:07,629 38914 INFO odoo odoo.modules.loading: loading mail/data/ir_config_parameter_data.xml 
2025-07-31 10:44:07,632 38914 INFO odoo odoo.modules.loading: loading mail/data/res_partner_data.xml 
2025-07-31 10:44:07,661 38914 INFO odoo odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 6 connections  
2025-07-31 10:44:08,230 38914 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:44:08,230 38914 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:44:08,230 38914 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:44:08,230 38914 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:44:08,319 38914 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:44:08,432 38914 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:44:08,453 38914 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:44:08,457 38914 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:44:08,462 38914 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:44:08,492 38914 INFO odoo odoo.modules.loading: 11 modules loaded in 0.03s, 0 queries (+0 extra) 
2025-07-31 10:44:08,509 38914 ERROR odoo odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:44:08,509 38914 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:44:08,511 38914 INFO odoo odoo.modules.registry: Registry loaded in 0.082s 
2025-07-31 10:44:10,313 38914 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:44:10,341 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:44:10] "GET /web/service-worker.js HTTP/1.1" 200 - 8 0.012 0.018
2025-07-31 10:49:46,986 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:46] "GET /web?reload=true HTTP/1.1" 200 - 70 0.068 0.543
2025-07-31 10:49:47,287 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.006
2025-07-31 10:49:47,311 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.010 0.009
2025-07-31 10:49:47,534 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.192
2025-07-31 10:49:47,542 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.007 0.194
2025-07-31 10:49:47,564 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:49:47,564 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:49:47,566 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:49:47,566 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:49:47,567 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:49:47,569 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:49:47,569 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:49:47,571 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:49:47,571 38914 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:49:47,593 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.307
2025-07-31 10:49:47,641 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.023 0.017
2025-07-31 10:49:47,661 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.013
2025-07-31 10:49:47,697 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.038
2025-07-31 10:49:47,703 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.035 0.020
2025-07-31 10:49:47,780 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /account_ledger/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-07-31 10:49:47,783 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /jgq_reportbro/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-07-31 10:49:47,784 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /spiffy_theme_backend/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.001
2025-07-31 10:49:47,814 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /expense_reimburse/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-07-31 10:49:47,815 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /izi_dashboard/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.009
2025-07-31 10:49:47,816 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /business_trip/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.020
2025-07-31 10:49:47,817 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /mommy_base/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /sale_purchase_advanced_with_contact/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /purchase_request/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-31 10:49:47,826 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /mommy_payment_alipay/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.011
2025-07-31 10:49:47,852 38914 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:47] "GET /web_approval/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:49:47,855 38914 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:49:49,625 38914 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:49:49] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.009
2025-07-31 10:52:11,709 44575 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:52:11,709 44575 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:52:11,709 44575 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:52:11,709 44575 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:52:11,982 44575 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:52:12,237 44575 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:52:46,154 44575 INFO ? odoo.service.server: Initiating shutdown 
2025-07-31 10:52:46,156 44575 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-07-31 10:52:46,316 44575 INFO ? odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 2 connections  
2025-07-31 10:52:52,176 44728 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:52:52,176 44728 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:52:52,176 44728 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:52:52,176 44728 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:52:52,238 44728 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:52:52,359 44728 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:53:39,151 44728 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-31 10:53:39,159 44728 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:53:39,173 44728 INFO ? odoo.modules.loading: loading 11 modules... 
2025-07-31 10:53:39,436 44728 INFO ? odoo.modules.loading: 11 modules loaded in 0.26s, 0 queries (+0 extra) 
2025-07-31 10:53:39,462 44728 ERROR ? odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:53:39,462 44728 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-31 10:53:39,467 44728 INFO ? odoo.modules.registry: Registry loaded in 0.357s 
2025-07-31 10:53:39,468 44728 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:53:39,481 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:39] "GET / HTTP/1.1" 303 - 21 0.032 0.342
2025-07-31 10:53:39,956 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:39] "GET /web HTTP/1.1" 200 - 71 0.073 0.395
2025-07-31 10:53:40,498 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.006
2025-07-31 10:53:40,730 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.009 0.014
2025-07-31 10:53:40,749 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.003
2025-07-31 10:53:40,764 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:40] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.003
2025-07-31 10:53:40,972 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:53:40,973 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:53:40,974 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:53:40,975 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:53:40,975 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:53:40,977 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:53:40,977 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:53:40,978 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:53:40,978 44728 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:53:41,002 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.282
2025-07-31 10:53:41,072 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.038 0.025
2025-07-31 10:53:41,075 44728 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:53:41,119 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.023
2025-07-31 10:53:41,158 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.053
2025-07-31 10:53:41,158 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:41] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.033 0.031
2025-07-31 10:53:43,008 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:43] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.011
2025-07-31 10:53:46,593 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/action_get HTTP/1.1" 200 - 9 0.004 0.009
2025-07-31 10:53:46,686 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 25 0.022 0.064
2025-07-31 10:53:46,743 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 11 0.008 0.040
2025-07-31 10:53:46,800 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "GET /web/bundle/web_editor.backend_assets_wysiwyg?lang=zh_CN HTTP/1.1" 200 - 7 0.002 0.026
2025-07-31 10:53:46,860 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 8 0.005 0.021
2025-07-31 10:53:46,926 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:46] "GET /web/image?model=res.users&id=2&field=avatar_128&unique=1753959226757 HTTP/1.1" 200 - 9 0.006 0.017
2025-07-31 10:53:50,717 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/action/load HTTP/1.1" 200 - 8 0.007 0.017
2025-07-31 10:53:50,774 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 7 0.010 0.029
2025-07-31 10:53:50,856 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 26 0.026 0.042
2025-07-31 10:53:50,896 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.002 0.007
2025-07-31 10:53:50,915 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:50] "POST /base_setup/data HTTP/1.1" 200 - 6 0.005 0.017
2025-07-31 10:53:54,660 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:53:54] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 8 0.008 0.024
2025-07-31 10:54:03,652 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.008
2025-07-31 10:54:03,692 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.009 0.040
2025-07-31 10:54:03,704 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:03] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.042 0.019
2025-07-31 10:54:06,972 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:06] "POST /web/dataset/call_kw/res.users/action_get HTTP/1.1" 200 - 8 0.003 0.012
2025-07-31 10:54:07,025 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:07] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 11 0.006 0.037
2025-07-31 10:54:07,079 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:07] "GET /web/image?model=res.users&id=2&field=avatar_128&unique=1753959247029 HTTP/1.1" 200 - 9 0.004 0.016
2025-07-31 10:54:19,073 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.037 0.058
2025-07-31 10:54:19,103 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:19] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.016
2025-07-31 10:54:21,328 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:21,329 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:54:21,330 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:21,381 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:54:21,558 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:54:21,558 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:21] "POST /web/dataset/call_button HTTP/1.1" 200 - 12 0.013 0.230
2025-07-31 10:54:33,941 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:33,941 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:54:33,942 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:54:33,982 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:54:33,985 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:54:33,985 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:54:33] "POST /web/dataset/call_button HTTP/1.1" 200 - 11 0.013 0.055
2025-07-31 10:55:43,975 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:43] "GET /web HTTP/1.1" 200 - 9 0.009 0.032
2025-07-31 10:55:44,425 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.006 0.007
2025-07-31 10:55:44,429 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/action/load HTTP/1.1" 200 - 9 0.009 0.010
2025-07-31 10:55:44,442 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.003 0.013
2025-07-31 10:55:44,486 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.017
2025-07-31 10:55:44,486 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.001 0.017
2025-07-31 10:55:44,494 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 5 0.016 0.009
2025-07-31 10:55:44,496 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.004
2025-07-31 10:55:44,527 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.020 0.016
2025-07-31 10:55:44,539 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:44] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.041
2025-07-31 10:55:46,525 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:46] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.008
2025-07-31 10:55:47,517 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:47] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.046 0.071
2025-07-31 10:55:47,552 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:47] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.018
2025-07-31 10:55:48,644 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:55:48,645 44728 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:55:48,647 44728 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:55:48,690 44728 WARNING odoo odoo.modules.module: DistributionNotFound: The 'sqlparse' distribution was not found and is required by the application 
2025-07-31 10:55:48,693 44728 WARNING odoo odoo.http: 不能安装模块“izi_data”，因为一个外部的依赖没有满足：Python library not installed: sqlparse 
2025-07-31 10:55:48,694 44728 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:55:48] "POST /web/dataset/call_button HTTP/1.1" 200 - 11 0.010 0.051
2025-07-31 10:56:37,851 45618 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:56:37,851 45618 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:56:37,851 45618 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:56:37,851 45618 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:56:37,915 45618 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:56:38,024 45618 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:56:43,411 45618 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:43,424 45618 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-31 10:56:43,435 45618 INFO ? odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:43,491 45618 INFO ? odoo.modules.loading: 11 modules loaded in 0.06s, 0 queries (+0 extra) 
2025-07-31 10:56:43,512 45618 ERROR ? odoo.modules.loading: Some modules have inconsistent states, some dependencies may be missing: ['analytic', 'auth_signup', 'auth_totp_mail', 'base_install_request', 'google_gmail', 'iap_mail', 'mail', 'mail_bot', 'partner_autocomplete', 'phone_validation', 'privacy_lookup', 'sms', 'snailmail', 'uom'] 
2025-07-31 10:56:43,512 45618 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-31 10:56:43,515 45618 INFO ? odoo.modules.registry: Registry loaded in 0.151s 
2025-07-31 10:56:43,516 45618 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:56:43,554 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:43] "GET / HTTP/1.1" 303 - 21 0.054 0.141
2025-07-31 10:56:44,107 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web HTTP/1.1" 200 - 71 0.066 0.480
2025-07-31 10:56:44,619 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.009
2025-07-31 10:56:44,838 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.010 0.014
2025-07-31 10:56:44,860 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.004
2025-07-31 10:56:44,875 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:44] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.003
2025-07-31 10:56:45,109 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:56:45,110 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:56:45,112 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:56:45,113 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:56:45,113 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:56:45,115 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:56:45,116 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:56:45,116 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:56:45,118 45618 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:56:45,134 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.312
2025-07-31 10:56:45,182 45618 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:56:45,191 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.027 0.023
2025-07-31 10:56:45,220 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.022
2025-07-31 10:56:45,249 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.021 0.031
2025-07-31 10:56:45,263 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.011 0.052
2025-07-31 10:56:47,093 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:47] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.005
2025-07-31 10:56:49,046 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:49] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.044 0.049
2025-07-31 10:56:49,068 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:49] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.007 0.008
2025-07-31 10:56:50,198 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:56:50,199 45618 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:56:50,200 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:56:50,520 45618 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:50,524 45618 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,534 45618 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:56:50,535 45618 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:56:50,866 45618 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:50,870 45618 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,871 45618 INFO odoo odoo.modules.loading: loading 27 modules... 
2025-07-31 10:56:50,871 45618 INFO odoo odoo.modules.loading: Loading module izi_data (2/27) 
2025-07-31 10:56:50,881 45618 WARNING odoo py.warnings: /Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/controllers/controllers.py:76: SyntaxWarning: "is" with a literal. Did you mean "=="?
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 973, in _bootstrap
    self._bootstrap_inner()
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
    self.run()
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/threading.py", line 953, in run
    self._target(*self._args, **self._kwargs)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/server.py", line 127, in __init__
    super().__init__(*args, **kwargs)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socketserver.py", line 747, in __init__
    self.handle()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 342, in handle
    BaseHTTPRequestHandler.handle(self)
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/http/server.py", line 433, in handle
    self.handle_one_request()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 374, in handle_one_request
    self.run_wsgi()
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 319, in run_wsgi
    execute(self.server.app)
  File "/Users/<USER>/MelGeek/odoo-17.0/.venv/lib/python3.10/site-packages/werkzeug/serving.py", line 308, in execute
    application_iter = app(environ, start_response)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2375, in __call__
    response = request._serve_db()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1950, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/model.py", line 152, in retrying
    result = func()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1978, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2182, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 786, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 29, in call_button
    action = self._call_kw(model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 21, in _call_kw
    return call_kw(Model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 484, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 469, in _call_kw_multi
    result = method(recs, *args, **kwargs)
  File "<decorator-gen-77>", line 2, in button_immediate_install
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 469, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 593, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "<decorator-gen-16>", line 2, in new
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/controllers/__init__.py", line 5, in <module>
    from . import controllers
  File "/opt/homebrew/Cellar/python@3.10/3.10.18/Frameworks/Python.framework/Versions/3.10/lib/python3.10/warnings.py", line 109, in _showwarnmsg
    sw(msg.message, msg.category, msg.filename, msg.lineno,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/netsvc.py", line 321, in showwarning_with_traceback
    for frame in traceback.extract_stack():
 
2025-07-31 10:56:50,883 45618 CRITICAL odoo odoo.modules.module: Couldn't load module izi_data 
2025-07-31 10:56:50,885 45618 WARNING odoo odoo.modules.loading: Transient module states were reset 
2025-07-31 10:56:50,885 45618 ERROR odoo odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/__init__.py", line 4, in <module>
    from . import common, wizard
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/__init__.py", line 4, in <module>
    from . import izi_tools
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/izi_tools.py", line 12, in <module>
    import pandas
ModuleNotFoundError: No module named 'pandas'
2025-07-31 10:56:50,887 45618 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:56:50,890 45618 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,891 45618 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:56:50,894 45618 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:56:50,905 45618 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:56:50,906 45618 INFO odoo odoo.modules.registry: Registry loaded in 0.021s 
2025-07-31 10:56:50,907 45618 ERROR odoo odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2375, in __call__
    response = request._serve_db()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1950, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/service/model.py", line 152, in retrying
    result = func()
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 1978, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 2182, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/http.py", line 786, in route_wrapper
    result = endpoint(self, *args, **params_ok)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 29, in call_button
    action = self._call_kw(model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/web/controllers/dataset.py", line 21, in _call_kw
    return call_kw(Model, method, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 484, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/api.py", line 469, in _call_kw_multi
    result = method(recs, *args, **kwargs)
  File "<decorator-gen-77>", line 2, in button_immediate_install
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 469, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/addons/base/models/ir_module.py", line 593, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
  File "<decorator-gen-16>", line 2, in new
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/Users/<USER>/MelGeek/odoo-17.0/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/__init__.py", line 4, in <module>
    from . import controllers, models, tests
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/__init__.py", line 4, in <module>
    from . import common, wizard
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/__init__.py", line 4, in <module>
    from . import izi_tools
  File "/Users/<USER>/MelGeek/odoo-17.0/addons/izi_data/models/common/izi_tools.py", line 12, in <module>
    import pandas
ModuleNotFoundError: No module named 'pandas'
2025-07-31 10:56:50,907 45618 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:56:50] "POST /web/dataset/call_button HTTP/1.1" 200 - 1521 0.268 0.452
2025-07-31 10:59:12,762 46743 INFO ? odoo: Odoo version 17.0 
2025-07-31 10:59:12,762 46743 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-07-31 10:59:12,762 46743 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-07-31 10:59:12,762 46743 INFO ? odoo: database: odoo@localhost:5432 
2025-07-31 10:59:12,825 46743 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-31 10:59:12,945 46743 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-07-31 10:59:14,235 46743 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-31 10:59:14,239 46743 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:59:14,246 46743 INFO ? odoo.modules.loading: loading 11 modules... 
2025-07-31 10:59:14,294 46743 INFO ? odoo.modules.loading: 11 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-07-31 10:59:14,318 46743 INFO ? odoo.modules.loading: Modules loaded. 
2025-07-31 10:59:14,322 46743 INFO ? odoo.modules.registry: Registry loaded in 0.110s 
2025-07-31 10:59:14,324 46743 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:59:14,868 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:14] "GET /web HTTP/1.1" 200 - 89 0.087 0.570
2025-07-31 10:59:15,190 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.002 0.007
2025-07-31 10:59:15,210 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.007 0.008
2025-07-31 10:59:15,277 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.009 0.019
2025-07-31 10:59:15,279 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.014 0.016
2025-07-31 10:59:15,524 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-07-31 10:59:15,524 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-07-31 10:59:15,526 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-07-31 10:59:15,527 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-07-31 10:59:15,527 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-07-31 10:59:15,529 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-07-31 10:59:15,530 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-07-31 10:59:15,530 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-07-31 10:59:15,531 46743 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-07-31 10:59:15,547 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.359
2025-07-31 10:59:15,589 46743 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-31 10:59:15,600 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.028 0.018
2025-07-31 10:59:15,628 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.021
2025-07-31 10:59:15,660 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.027 0.027
2025-07-31 10:59:15,667 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:15] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.010 0.051
2025-07-31 10:59:17,578 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:17] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.007
2025-07-31 10:59:17,879 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:17] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 57 0.043 0.058
2025-07-31 10:59:17,910 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:17] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.013
2025-07-31 10:59:19,019 46743 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:59:19,020 46743 INFO odoo odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-07-31 10:59:19,022 46743 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['IZI Dashboard with AI'] to user admin #2 via 127.0.0.1 
2025-07-31 10:59:19,387 46743 INFO odoo odoo.modules.loading: loading 1 modules... 
2025-07-31 10:59:19,390 46743 INFO odoo odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:59:19,402 46743 INFO odoo odoo.modules.loading: updating modules list 
2025-07-31 10:59:19,404 46743 INFO odoo odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-07-31 10:59:19,710 46743 INFO odoo odoo.modules.loading: loading 11 modules... 
2025-07-31 10:59:19,713 46743 INFO odoo odoo.modules.loading: 11 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-31 10:59:19,714 46743 INFO odoo odoo.modules.loading: loading 13 modules... 
2025-07-31 10:59:19,714 46743 INFO odoo odoo.modules.loading: Loading module izi_data (2/13) 
2025-07-31 10:59:20,108 46743 INFO odoo odoo.modules.registry: module izi_data: creating or updating database tables 
2025-07-31 10:59:20,607 46743 INFO odoo odoo.modules.loading: loading izi_data/security/res_groups.xml 
2025-07-31 10:59:20,720 46743 INFO odoo odoo.modules.loading: loading izi_data/data/izi_table_field_mapping_db_odoo.xml 
2025-07-31 10:59:20,744 46743 INFO odoo odoo.modules.loading: loading izi_data/data/izi_analysis_filter_operator_db_odoo.xml 
2025-07-31 10:59:20,764 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/izi_data_source.xml 
2025-07-31 10:59:20,780 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/izi_table.xml 
2025-07-31 10:59:20,849 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/izi_analysis.xml 
2025-07-31 10:59:20,904 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/ir_attachment.xml 
2025-07-31 10:59:20,917 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/ir_cron.xml 
2025-07-31 10:59:20,923 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/izi_kpi.xml 
2025-07-31 10:59:21,006 46743 INFO odoo odoo.modules.loading: loading izi_data/views/common/izi_kpi_line.xml 
2025-07-31 10:59:21,048 46743 INFO odoo odoo.modules.loading: loading izi_data/views/action/action_menu.xml 
2025-07-31 10:59:21,079 46743 INFO odoo odoo.modules.loading: loading izi_data/views/menu.xml 
2025-07-31 10:59:21,192 46743 INFO odoo odoo.modules.loading: loading izi_data/security/ir.model.access.csv 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Table Field Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Table Field Mapping Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Table Python Code Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Metric Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Dimension Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Drilldown Dimension Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Filter Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,250 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Filter Operator Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,251 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Sort Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,251 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI KPI Category Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,251 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI KPI Period Access for Group User: CRUD has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,251 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Analysis Category Read has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,251 46743 WARNING odoo odoo.addons.base.models.ir_model: Rule IZI Data Source Item has no group, this is a deprecated feature. Every access-granting rule should specify a group. 
2025-07-31 10:59:21,277 46743 INFO odoo odoo.addons.base.models.ir_module: module izi_data: no translation for language zh_CN 
2025-07-31 10:59:21,314 46743 INFO odoo odoo.modules.loading: Module izi_data loaded in 1.60s, 1029 queries (+1029 other) 
2025-07-31 10:59:21,314 46743 INFO odoo odoo.modules.loading: Loading module izi_dashboard (9/13) 
2025-07-31 10:59:21,398 46743 INFO odoo odoo.modules.registry: module izi_dashboard: creating or updating database tables 
2025-07-31 10:59:22,251 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/security/res_groups.xml 
2025-07-31 10:59:22,340 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/data/izi_visual_type.xml 
2025-07-31 10:59:22,372 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/data/izi_visual_config.xml 
2025-07-31 10:59:22,452 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/data/izi_visual_config_value.xml 
2025-07-31 10:59:22,503 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/data/izi_dashboard_theme.xml 
2025-07-31 10:59:22,566 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/data/izi_data_template.xml 
2025-07-31 10:59:25,883 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/common/izi_dashboard.xml 
2025-07-31 10:59:25,924 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/common/izi_analysis.xml 
2025-07-31 10:59:25,962 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/common/izi_lab_api_key_wizard.xml 
2025-07-31 10:59:25,972 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/common/res_company.xml 
2025-07-31 10:59:25,985 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/wizard/izi_dashboard_config_wizard.xml 
2025-07-31 10:59:26,051 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/action/action_menu.xml 
2025-07-31 10:59:26,094 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/menu.xml 
2025-07-31 10:59:26,127 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/security/ir.model.access.csv 
2025-07-31 10:59:26,217 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/template/izi_dashboard.xml 
2025-07-31 10:59:26,233 46743 INFO odoo odoo.modules.loading: loading izi_dashboard/views/template/izi_dashboard_slide.xml 
2025-07-31 10:59:26,258 46743 INFO odoo odoo.addons.base.models.ir_module: module izi_dashboard: no translation for language zh_CN 
2025-07-31 10:59:26,314 46743 INFO odoo odoo.modules.loading: Module izi_dashboard loaded in 5.00s, 3084 queries (+3084 other) 
2025-07-31 10:59:26,314 46743 INFO odoo odoo.modules.loading: 13 modules loaded in 6.60s, 4113 queries (+4113 extra) 
2025-07-31 10:59:26,384 46743 INFO odoo odoo.modules.loading: Model uom.uom is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-31 10:59:26,384 46743 INFO odoo odoo.modules.loading: Model uom.category is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-31 10:59:26,618 46743 INFO odoo odoo.modules.loading: Modules loaded. 
2025-07-31 10:59:26,625 46743 INFO odoo odoo.modules.registry: Registry loaded in 7.253s 
2025-07-31 10:59:26,626 46743 INFO odoo odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-07-31 10:59:26,633 46743 INFO odoo odoo.modules.registry: Registry changed, signaling through the database 
2025-07-31 10:59:26,635 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:26] "POST /web/dataset/call_button HTTP/1.1" 200 - 6144 4.073 3.558
2025-07-31 10:59:26,669 46743 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-31 10:59:26,790 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:26] "GET /web HTTP/1.1" 200 - 73 0.025 0.103
2025-07-31 10:59:27,533 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /web/webclient/load_menus/1fbb8d53ea109792f3329087f8a3fd1a84012a05b7be4ff04ed2939ee08b642a HTTP/1.1" 200 - 2 0.004 0.667
2025-07-31 10:59:27,541 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /web/webclient/translations/884607bda48db46d5087c1dcec9ccb31b1799244?lang=zh_CN HTTP/1.1" 200 - 3 0.568 0.107
2025-07-31 10:59:27,583 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 2 0.003 0.019
2025-07-31 10:59:27,587 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.026 0.020
2025-07-31 10:59:27,595 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /web HTTP/1.1" 200 - 9 0.027 0.036
2025-07-31 10:59:27,679 46743 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/b07aa92/web.assets_web.min.css (id:200) 
2025-07-31 10:59:27,679 46743 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [192] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/b07aa92/web.assets_web.min.css 
2025-07-31 10:59:27,699 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:27] "GET /web/assets/b07aa92/web.assets_web.min.css HTTP/1.1" 200 - 16 0.063 0.829
2025-07-31 10:59:28,474 46743 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/82f533f/web.assets_web.min.js (id:201) 
2025-07-31 10:59:28,475 46743 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [193] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/82f533f/web.assets_web.min.js 
2025-07-31 10:59:28,497 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:28] "GET /web/assets/82f533f/web.assets_web.min.js HTTP/1.1" 200 - 13 0.023 1.666
2025-07-31 10:59:28,824 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:28] "GET /web/webclient/translations/884607bda48db46d5087c1dcec9ccb31b1799244?lang=zh_CN HTTP/1.1" 200 - 2 0.001 0.012
2025-07-31 10:59:29,308 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 200 - 4 0.002 0.009
2025-07-31 10:59:29,321 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.004
2025-07-31 10:59:29,355 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.015 0.016
2025-07-31 10:59:29,357 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/action/load HTTP/1.1" 200 - 8 0.017 0.027
2025-07-31 10:59:29,362 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 9 0.030 0.018
2025-07-31 10:59:29,420 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 5 0.006 0.028
2025-07-31 10:59:29,428 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.004
2025-07-31 10:59:29,445 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard/get_views HTTP/1.1" 200 - 24 0.048 0.025
2025-07-31 10:59:29,472 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /izi_dashboard/static/lib/google/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUce.woff2 HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:59:29,474 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "GET /izi_dashboard/static/lib/google/sykg-zNym6YjUruM-QrEh7-nyTnjDwKNJ_190Fjzag.woff2 HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:59:29,498 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard.theme/search_read HTTP/1.1" 200 - 3 0.031 0.008
2025-07-31 10:59:29,501 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard/get_user_groups HTTP/1.1" 200 - 6 0.006 0.034
2025-07-31 10:59:29,505 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 12 0.009 0.036
2025-07-31 10:59:29,523 46743 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-07-31 10:59:29,525 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 6 0.008 0.008
2025-07-31 10:59:29,525 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 3 0.004 0.007
2025-07-31 10:59:29,547 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:29,549 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:29,565 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 13 0.017 0.010
2025-07-31 10:59:29,566 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 14 0.017 0.011
2025-07-31 10:59:29,595 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 47 0.036 0.018
2025-07-31 10:59:29,596 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:29] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 39 0.039 0.018
2025-07-31 10:59:31,920 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:31] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.006
2025-07-31 10:59:32,867 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:32] "POST /web/dataset/call_kw/izi.dashboard.category/search_read HTTP/1.1" 200 - 3 0.006 0.006
2025-07-31 10:59:32,869 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:32] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 5 0.005 0.010
2025-07-31 10:59:35,114 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:35] "POST /web/dataset/call_kw/izi.dashboard/get_views HTTP/1.1" 200 - 31 0.018 0.070
2025-07-31 10:59:35,141 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:35] "POST /web/dataset/call_kw/izi.dashboard/onchange HTTP/1.1" 200 - 5 0.002 0.010
2025-07-31 10:59:35,194 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:35] "GET /izi_dashboard/static/src/css/font/Roboto/Roboto-Regular.ttf HTTP/1.1" 200 - 0 0.000 0.005
2025-07-31 10:59:35,194 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:35] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:59:38,047 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:38] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 5 0.007 0.021
2025-07-31 10:59:39,001 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:39] "POST /web/dataset/call_kw/izi.dashboard/onchange HTTP/1.1" 200 - 3 0.002 0.008
2025-07-31 10:59:39,414 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:39] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 5 0.005 0.012
2025-07-31 10:59:40,845 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:40] "POST /web/dataset/call_kw/izi.dashboard/onchange HTTP/1.1" 200 - 3 0.002 0.010
2025-07-31 10:59:42,808 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:42] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 5 0.003 0.009
2025-07-31 10:59:43,747 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:43] "POST /web/dataset/call_kw/izi.dashboard/web_read HTTP/1.1" 200 - 17 0.010 0.013
2025-07-31 10:59:44,264 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:44] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 5 0.005 0.018
2025-07-31 10:59:44,975 46743 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-07-31 10:59:44,979 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:44] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 2 0.005 0.012
2025-07-31 10:59:44,981 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:44] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 5 0.004 0.018
2025-07-31 10:59:45,014 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:45,022 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:45,023 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:45] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.015 0.018
2025-07-31 10:59:45,024 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:45] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.012 0.019
2025-07-31 10:59:45,036 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:45] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.016 0.021
2025-07-31 10:59:45,052 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:45] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.028 0.027
2025-07-31 10:59:47,538 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/get_views HTTP/1.1" 200 - 17 0.010 0.030
2025-07-31 10:59:47,559 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.006
2025-07-31 10:59:47,566 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 3 0.006 0.012
2025-07-31 10:59:47,570 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.005 0.014
2025-07-31 10:59:47,574 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.005 0.015
2025-07-31 10:59:47,584 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:47,597 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.004 0.019
2025-07-31 10:59:47,610 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 23 0.021 0.014
2025-07-31 10:59:47,611 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.020 0.014
2025-07-31 10:59:47,622 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.021 0.023
2025-07-31 10:59:47,623 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.021 0.022
2025-07-31 10:59:47,623 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 13 0.026 0.020
2025-07-31 10:59:47,626 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.015 0.011
2025-07-31 10:59:47,631 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.006 0.010
2025-07-31 10:59:47,692 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:47] "GET /izi_dashboard/static/lib/select2/select2.png HTTP/1.1" 200 - 0 0.000 0.001
2025-07-31 10:59:50,845 46743 INFO odoo odoo.models.unlink: User #2 deleted izi.analysis.visual.config records with IDs: [4, 5] 
2025-07-31 10:59:50,863 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:50] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_type HTTP/1.1" 200 - 25 0.024 0.032
2025-07-31 10:59:50,877 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:50,894 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:50] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.012 0.011
2025-07-31 10:59:50,923 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:50] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.023 0.032
2025-07-31 10:59:58,182 46743 INFO odoo odoo.models.unlink: User #2 deleted izi.analysis.visual.config records with IDs: [6, 7, 8, 9] 
2025-07-31 10:59:58,196 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_type HTTP/1.1" 200 - 18 0.012 0.022
2025-07-31 10:59:58,208 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:58,226 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.010 0.015
2025-07-31 10:59:58,259 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.028 0.029
2025-07-31 10:59:58,366 46743 INFO odoo odoo.models.unlink: User #2 deleted izi.analysis.visual.config records with IDs: [10, 11, 12, 13] 
2025-07-31 10:59:58,374 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_type HTTP/1.1" 200 - 18 0.007 0.008
2025-07-31 10:59:58,383 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 10:59:58,417 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.022 0.015
2025-07-31 10:59:58,435 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 10:59:58] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.024 0.034
2025-07-31 11:00:00,147 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:00,177 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:00] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.014 0.029
2025-07-31 11:00:14,508 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:14] "POST /web/action/load HTTP/1.1" 200 - 8 0.008 0.019
2025-07-31 11:00:14,562 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:14] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 8 0.012 0.028
2025-07-31 11:00:14,632 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:14] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 27 0.019 0.038
2025-07-31 11:00:14,682 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:14] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.010 0.007
2025-07-31 11:00:14,688 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:14] "POST /base_setup/data HTTP/1.1" 200 - 6 0.005 0.011
2025-07-31 11:00:18,164 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:18] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 8 0.011 0.029
2025-07-31 11:00:22,487 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:22] "POST /web/action/load HTTP/1.1" 200 - 9 0.009 0.018
2025-07-31 11:00:22,623 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:22] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 40 0.039 0.081
2025-07-31 11:00:22,670 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:22] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.006 0.023
2025-07-31 11:00:22,715 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:22] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.044 0.028
2025-07-31 11:00:22,720 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:22] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.010 0.065
2025-07-31 11:00:26,326 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.dashboard.theme/search_read HTTP/1.1" 200 - 2 0.002 0.017
2025-07-31 11:00:26,336 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.dashboard/get_user_groups HTTP/1.1" 200 - 4 0.007 0.018
2025-07-31 11:00:26,338 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 6 0.009 0.021
2025-07-31 11:00:26,351 46743 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-07-31 11:00:26,355 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 5 0.004 0.009
2025-07-31 11:00:26,355 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 2 0.002 0.010
2025-07-31 11:00:26,370 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:26,376 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:26,380 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.006 0.010
2025-07-31 11:00:26,382 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.006 0.013
2025-07-31 11:00:26,394 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.016 0.014
2025-07-31 11:00:26,398 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:26] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.015 0.019
2025-07-31 11:00:30,159 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "GET /izi_dashboard/static/lib/ace-1.3.1/ace.js HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 11:00:30,171 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "GET /izi_dashboard/static/lib/ace-1.3.1/mode-sql.js HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 11:00:30,172 46743 INFO ? werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "GET /izi_dashboard/static/lib/ace-1.3.1/theme-chrome.js HTTP/1.1" 200 - 0 0.000 0.003
2025-07-31 11:00:30,196 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.006 0.012
2025-07-31 11:00:30,199 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "POST /web/dataset/call_kw/izi.analysis.category/search_read HTTP/1.1" 200 - 3 0.008 0.012
2025-07-31 11:00:30,200 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:30] "POST /web/dataset/call_kw/izi.analysis/ui_get_all HTTP/1.1" 200 - 6 0.011 0.012
2025-07-31 11:00:36,187 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.dashboard.block/create HTTP/1.1" 200 - 15 0.011 0.014
2025-07-31 11:00:36,199 46743 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-07-31 11:00:36,203 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 2 0.003 0.006
2025-07-31 11:00:36,211 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 5 0.005 0.013
2025-07-31 11:00:36,245 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:36,247 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:36,251 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.010 0.024
2025-07-31 11:00:36,253 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:36,253 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.011 0.024
2025-07-31 11:00:36,262 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.013 0.028
2025-07-31 11:00:36,282 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.024 0.031
2025-07-31 11:00:36,283 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.028 0.032
2025-07-31 11:00:36,284 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:36] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.023 0.036
2025-07-31 11:00:40,338 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.002 0.009
2025-07-31 11:00:40,343 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.004 0.011
2025-07-31 11:00:40,344 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.008 0.009
2025-07-31 11:00:40,356 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:40,375 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.011 0.016
2025-07-31 11:00:40,387 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.018 0.019
2025-07-31 11:00:40,395 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.026 0.021
2025-07-31 11:00:40,401 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.024 0.025
2025-07-31 11:00:40,406 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.025 0.028
2025-07-31 11:00:40,415 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.015 0.016
2025-07-31 11:00:40,418 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.012 0.015
2025-07-31 11:00:40,426 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:40] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.058 0.019
2025-07-31 11:00:47,881 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:47] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 18 0.020 0.026
2025-07-31 11:00:47,894 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:47,950 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:47] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.019 0.043
2025-07-31 11:00:48,001 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:48,051 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:48] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.019 0.039
2025-07-31 11:00:50,698 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.002 0.015
2025-07-31 11:00:50,703 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.008 0.010
2025-07-31 11:00:50,705 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.009 0.013
2025-07-31 11:00:50,726 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:50,734 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.012 0.015
2025-07-31 11:00:50,738 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.012 0.016
2025-07-31 11:00:50,752 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.024 0.019
2025-07-31 11:00:50,760 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.025 0.024
2025-07-31 11:00:50,763 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.027 0.023
2025-07-31 11:00:50,772 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.035 0.028
2025-07-31 11:00:50,775 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.022 0.014
2025-07-31 11:00:50,775 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.018 0.015
2025-07-31 11:00:56,210 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:56] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.008 0.022
2025-07-31 11:00:56,222 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:56,261 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:56] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.013 0.032
2025-07-31 11:00:58,404 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:58] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.008 0.024
2025-07-31 11:00:58,416 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:00:58,458 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:00:58] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.014 0.032
2025-07-31 11:01:00,041 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:00,085 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:00] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.015 0.040
2025-07-31 11:01:07,023 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:07] "POST /web/dataset/call_kw/izi.analysis/get_views HTTP/1.1" 200 - 32 0.011 0.034
2025-07-31 11:01:07,061 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:07] "POST /web/dataset/call_kw/izi.analysis/web_read HTTP/1.1" 200 - 19 0.006 0.008
2025-07-31 11:01:08,628 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:08] "POST /web/dataset/call_button HTTP/1.1" 200 - 1 0.002 0.007
2025-07-31 11:01:08,644 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:08,697 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:08] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.018 0.043
2025-07-31 11:01:11,375 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.009 0.011
2025-07-31 11:01:11,380 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.006 0.015
2025-07-31 11:01:11,382 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.007 0.015
2025-07-31 11:01:11,395 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:11,409 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.007 0.017
2025-07-31 11:01:11,418 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.014 0.017
2025-07-31 11:01:11,425 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.018 0.022
2025-07-31 11:01:11,434 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.024 0.021
2025-07-31 11:01:11,442 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.030 0.021
2025-07-31 11:01:11,443 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.038 0.019
2025-07-31 11:01:11,448 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.015 0.014
2025-07-31 11:01:11,450 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:11] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.011 0.015
2025-07-31 11:01:13,016 46743 INFO odoo odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-07-31 11:01:13,100 46743 INFO odoo odoo.addons.base.models.ir_attachment: filestore gc 12 checked, 3 removed 
2025-07-31 11:01:13,166 46743 INFO odoo odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-07-31 11:01:13,231 46743 INFO odoo odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-07-31 11:01:13,266 46743 INFO odoo odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13] 
2025-07-31 11:01:13,317 46743 INFO odoo odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (0.300s). 
2025-07-31 11:01:22,925 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:22] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.007 0.018
2025-07-31 11:01:22,933 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:22,967 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:22] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.011 0.027
2025-07-31 11:01:33,744 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:33] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.008 0.020
2025-07-31 11:01:33,751 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:33,786 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:33] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.011 0.027
2025-07-31 11:01:47,774 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:47] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.011 0.027
2025-07-31 11:01:47,790 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:47,833 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:47] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.016 0.030
2025-07-31 11:01:55,734 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-07-31 11:01:55,796 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:01:55] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.026 0.044
2025-07-31 11:03:26,929 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:03:26] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.005
2025-07-31 11:20:07,045 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 11:20:07] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.006 0.013
2025-07-31 12:09:34,072 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 12:09:34] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.027
2025-07-31 13:16:26,272 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 13:16:26] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.001 0.014
2025-07-31 14:35:50,001 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 14:35:50] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.009
2025-07-31 15:57:21,472 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 15:57:21] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.003 0.016
2025-07-31 16:41:11,070 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 16:41:11] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.030 0.061
2025-07-31 18:03:24,969 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 18:03:24] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.021
2025-07-31 19:18:51,294 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 19:18:51] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.024
2025-07-31 19:29:32,809 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 19:29:32] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.007 0.026
2025-07-31 20:23:53,776 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 20:23:53] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.026
2025-07-31 22:04:39,008 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 22:04:39] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.003 0.017
2025-07-31 23:38:07,300 46743 INFO odoo werkzeug: 127.0.0.1 - - [31/Jul/2025 23:38:07] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.011 0.149
2025-08-01 00:54:18,342 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 00:54:18] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.010 0.018
2025-08-01 01:26:04,042 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:26:04] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.003 0.077
2025-08-01 01:26:23,400 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:26:23] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 2 0.046 0.189
2025-08-01 01:28:09,688 46743 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-01 01:28:09,713 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.006 0.035
2025-08-01 01:28:09,715 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.007 0.030
2025-08-01 01:28:09,716 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.009 0.032
2025-08-01 01:28:09,747 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:28:09,772 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.017 0.032
2025-08-01 01:28:09,784 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.020 0.037
2025-08-01 01:28:09,795 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.031 0.039
2025-08-01 01:28:09,799 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.028 0.043
2025-08-01 01:28:09,801 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.032 0.042
2025-08-01 01:28:09,808 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.017 0.014
2025-08-01 01:28:09,810 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.052 0.033
2025-08-01 01:28:09,813 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:09] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.007 0.011
2025-08-01 01:28:30,828 46743 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:28:30,902 46743 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:28:30] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.041 0.042
2025-08-01 01:40:40,164 97755 INFO ? odoo: Odoo version 17.0 
2025-08-01 01:40:40,164 97755 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-08-01 01:40:40,164 97755 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-08-01 01:40:40,164 97755 INFO ? odoo: database: odoo@localhost:5432 
2025-08-01 01:40:40,233 97755 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-08-01 01:40:40,356 97755 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-08-01 01:40:45,900 97755 INFO ? odoo.modules.loading: loading 1 modules... 
2025-08-01 01:40:45,915 97755 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-01 01:40:45,940 97755 INFO ? odoo.modules.loading: loading 13 modules... 
2025-08-01 01:40:46,393 97755 INFO ? odoo.modules.loading: 13 modules loaded in 0.45s, 0 queries (+0 extra) 
2025-08-01 01:40:46,427 97755 INFO ? odoo.modules.loading: Modules loaded. 
2025-08-01 01:40:46,433 97755 INFO ? odoo.modules.registry: Registry loaded in 0.575s 
2025-08-01 01:40:46,434 97755 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-08-01 01:40:46,991 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:46] "GET /web HTTP/1.1" 200 - 89 0.132 1.009
2025-08-01 01:40:47,943 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:40:47,943 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:40:47,943 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:40:47,943 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:40:47,944 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:40:47,945 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:40:47,945 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:40:47,945 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:40:47,945 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:40:47,945 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:40:47,950 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:40:47,951 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:40:47,951 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:40:47,951 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:40:47,951 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:40:47,954 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:40:47,954 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:40:47,954 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:40:47,954 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:40:47,954 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:40:47,955 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:40:47,955 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:40:47,956 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:40:47,956 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:40:47,956 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:40:47,961 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:40:47,961 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:40:47,961 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:40:47,961 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:40:47,961 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:40:47,964 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:40:47,964 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:40:47,964 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:40:47,964 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:40:47,965 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:40:47,966 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:40:47,966 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:40:47,966 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:40:47,966 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:40:47,967 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:40:47,969 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:40:47,969 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:40:47,969 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:40:47,969 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:40:47,969 97755 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:40:48,016 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:48] "GET /web/webclient/load_menus/1fbb8d53ea109792f3329087f8a3fd1a84012a05b7be4ff04ed2939ee08b642a HTTP/1.1" 200 - 2 0.002 0.769
2025-08-01 01:40:48,022 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:48] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 2 0.006 0.962
2025-08-01 01:40:48,027 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:48] "GET /web/webclient/translations/884607bda48db46d5087c1dcec9ccb31b1799244?lang=zh_CN HTTP/1.1" 200 - 3 0.010 0.771
2025-08-01 01:40:48,667 97755 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/039c98a/web.assets_web.min.css (id:202) 
2025-08-01 01:40:48,668 97755 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [200] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/039c98a/web.assets_web.min.css 
2025-08-01 01:40:48,714 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:48] "GET /web/assets/039c98a/web.assets_web.min.css HTTP/1.1" 200 - 17 0.118 1.539
2025-08-01 01:40:49,372 97755 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/2554bf4/web.assets_web.min.js (id:203) 
2025-08-01 01:40:49,372 97755 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [201] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/2554bf4/web.assets_web.min.js 
2025-08-01 01:40:49,389 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:49] "GET /web/assets/2554bf4/web.assets_web.min.js HTTP/1.1" 200 - 13 0.010 2.317
2025-08-01 01:40:49,733 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:49] "GET /web/webclient/translations/884607bda48db46d5087c1dcec9ccb31b1799244?lang=zh_CN HTTP/1.1" 200 - 2 0.008 0.011
2025-08-01 01:40:50,261 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 200 - 4 0.008 0.012
2025-08-01 01:40:50,269 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.005
2025-08-01 01:40:50,277 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/action/load HTTP/1.1" 200 - 8 0.027 0.014
2025-08-01 01:40:50,345 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 16 0.029 0.049
2025-08-01 01:40:50,402 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.004 0.034
2025-08-01 01:40:50,403 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.009
2025-08-01 01:40:50,427 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.027 0.032
2025-08-01 01:40:50,436 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.003
2025-08-01 01:40:50,438 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard/get_views HTTP/1.1" 200 - 25 0.043 0.040
2025-08-01 01:40:50,474 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /izi_dashboard/static/lib/google/sykg-zNym6YjUruM-QrEh7-nyTnjDwKNJ_190Fjzag.woff2 HTTP/1.1" 200 - 0 0.000 0.005
2025-08-01 01:40:50,474 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "GET /izi_dashboard/static/lib/google/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUce.woff2 HTTP/1.1" 200 - 0 0.000 0.004
2025-08-01 01:40:50,482 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard.theme/search_read HTTP/1.1" 200 - 3 0.010 0.005
2025-08-01 01:40:50,493 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard/get_user_groups HTTP/1.1" 200 - 6 0.011 0.013
2025-08-01 01:40:50,504 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 12 0.021 0.015
2025-08-01 01:40:50,519 97755 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-08-01 01:40:50,523 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 3 0.004 0.006
2025-08-01 01:40:50,533 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 6 0.018 0.008
2025-08-01 01:40:50,568 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:40:50,569 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:40:50,596 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:40:50,609 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 14 0.035 0.027
2025-08-01 01:40:50,610 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 14 0.039 0.024
2025-08-01 01:40:50,611 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 12 0.041 0.025
2025-08-01 01:40:50,666 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 46 0.077 0.041
2025-08-01 01:40:50,666 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 35 0.062 0.054
2025-08-01 01:40:50,667 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:50] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 44 0.085 0.034
2025-08-01 01:40:50,714 97755 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-01 01:40:53,146 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:40:53] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.011
2025-08-01 01:41:06,022 97755 INFO odoo odoo.models.unlink: User #2 deleted ir.model.data records with IDs: [9908] 
2025-08-01 01:41:06,022 97755 INFO odoo odoo.models.unlink: User #2 deleted izi.dashboard.block records with IDs: [1] 
2025-08-01 01:41:06,025 97755 INFO odoo odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-08-01 01:41:06,028 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:41:06] "POST /web/dataset/call_kw/izi.dashboard.block/unlink HTTP/1.1" 200 - 15 0.013 0.026
2025-08-01 01:42:04,544 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:04] "GET /izi_dashboard/static/src/css/font/Roboto/Roboto-Regular.ttf HTTP/1.1" 200 - 0 0.000 0.009
2025-08-01 01:42:06,626 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:06] "GET /web HTTP/1.1" 200 - 61 0.038 0.118
2025-08-01 01:42:07,376 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:07] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.011
2025-08-01 01:42:07,690 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:07] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.007
2025-08-01 01:42:09,984 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:09] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.007
2025-08-01 01:42:24,124 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/get_views HTTP/1.1" 200 - 24 0.015 0.050
2025-08-01 01:42:24,147 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.004
2025-08-01 01:42:24,163 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 3 0.009 0.018
2025-08-01 01:42:24,172 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 4 0.015 0.017
2025-08-01 01:42:24,182 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 9 0.021 0.023
2025-08-01 01:42:24,207 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:42:24,214 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 9 0.015 0.013
2025-08-01 01:42:24,235 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 22 0.030 0.015
2025-08-01 01:42:24,239 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 22 0.034 0.018
2025-08-01 01:42:24,245 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 20 0.033 0.022
2025-08-01 01:42:24,249 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 11 0.042 0.018
2025-08-01 01:42:24,249 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.024 0.009
2025-08-01 01:42:24,256 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "GET /izi_dashboard/static/lib/select2/select2.png HTTP/1.1" 200 - 0 0.000 0.007
2025-08-01 01:42:24,268 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.011 0.016
2025-08-01 01:42:24,285 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:24] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 48 0.062 0.035
2025-08-01 01:42:29,315 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:42:29,347 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:29] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.010 0.029
2025-08-01 01:42:32,279 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.013
2025-08-01 01:42:32,298 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.009 0.026
2025-08-01 01:42:32,302 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.009 0.028
2025-08-01 01:42:32,304 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.010 0.027
2025-08-01 01:42:32,318 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:42:32,342 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.017 0.014
2025-08-01 01:42:32,346 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.016 0.021
2025-08-01 01:42:32,372 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.047 0.016
2025-08-01 01:42:32,392 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.058 0.022
2025-08-01 01:42:32,393 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.055 0.024
2025-08-01 01:42:32,396 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.067 0.019
2025-08-01 01:42:32,405 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.026 0.035
2025-08-01 01:42:32,421 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:32] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.029 0.042
2025-08-01 01:42:40,874 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:40] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 19 0.015 0.028
2025-08-01 01:42:40,890 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:42:40,991 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:40] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.040 0.071
2025-08-01 01:42:43,836 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:42:43,865 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:43] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.011 0.028
2025-08-01 01:42:51,066 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:51] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.015 0.027
2025-08-01 01:42:51,138 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:51] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 41 0.046 0.054
2025-08-01 01:42:51,156 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:51] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.002 0.006
2025-08-01 01:42:51,203 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:51] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.037 0.018
2025-08-01 01:42:51,222 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:42:51] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 5 0.012 0.059
2025-08-01 01:43:37,534 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.015
2025-08-01 01:43:37,547 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.005 0.027
2025-08-01 01:43:37,549 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.004 0.026
2025-08-01 01:43:37,550 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.009 0.023
2025-08-01 01:43:37,572 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:43:37,579 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.007 0.013
2025-08-01 01:43:37,605 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.028 0.016
2025-08-01 01:43:37,605 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.024 0.020
2025-08-01 01:43:37,611 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.021 0.027
2025-08-01 01:43:37,619 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.037 0.021
2025-08-01 01:43:37,622 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.023 0.015
2025-08-01 01:43:37,624 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.041 0.024
2025-08-01 01:43:37,641 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:43:37] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.009 0.021
2025-08-01 01:44:51,668 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:44:51] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.007 0.029
2025-08-01 01:44:51,684 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:44:51,738 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:44:51] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.025 0.035
2025-08-01 01:44:55,839 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:44:55,862 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:44:55] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.012 0.030
2025-08-01 01:45:24,731 97755 INFO ? werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "GET /izi_dashboard/static/src/img/js.png HTTP/1.1" 200 - 0 0.000 0.012
2025-08-01 01:45:24,750 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.006 0.029
2025-08-01 01:45:24,753 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.012 0.023
2025-08-01 01:45:24,762 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.010 0.032
2025-08-01 01:45:24,779 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.007 0.012
2025-08-01 01:45:24,781 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:45:24,798 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.017 0.014
2025-08-01 01:45:24,800 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.020 0.016
2025-08-01 01:45:24,818 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.023 0.026
2025-08-01 01:45:24,818 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.026 0.022
2025-08-01 01:45:24,837 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.016 0.027
2025-08-01 01:45:24,841 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.012 0.024
2025-08-01 01:45:24,859 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:24] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.066 0.028
2025-08-01 01:45:29,978 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:29] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.007 0.016
2025-08-01 01:45:29,991 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:45:30,062 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:30] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.047 0.029
2025-08-01 01:45:33,814 97755 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:45:33,844 97755 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:45:33] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.014 0.026
2025-08-01 01:55:14,065 5588 INFO ? odoo: Odoo version 17.0 
2025-08-01 01:55:14,065 5588 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-08-01 01:55:14,065 5588 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-08-01 01:55:14,065 5588 INFO ? odoo: database: odoo@localhost:5432 
2025-08-01 01:55:14,150 5588 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-08-01 01:55:14,293 5588 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 
2025-08-01 01:55:19,051 5588 INFO ? odoo.modules.loading: loading 1 modules... 
2025-08-01 01:55:19,070 5588 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-08-01 01:55:19,091 5588 INFO ? odoo.modules.loading: loading 13 modules... 
2025-08-01 01:55:19,591 5588 INFO ? odoo.modules.loading: 13 modules loaded in 0.50s, 0 queries (+0 extra) 
2025-08-01 01:55:19,626 5588 INFO ? odoo.modules.loading: Modules loaded. 
2025-08-01 01:55:19,632 5588 INFO ? odoo.modules.registry: Registry loaded in 0.653s 
2025-08-01 01:55:19,634 5588 INFO odoo odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-08-01 01:55:20,273 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:20] "GET /web HTTP/1.1" 200 - 89 0.171 1.125
2025-08-01 01:55:21,215 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:55:21,215 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:55:21,215 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:55:21,215 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:55:21,215 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-08-01 01:55:21,216 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:55:21,216 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:55:21,216 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:55:21,217 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:55:21,217 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-08-01 01:55:21,222 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:55:21,222 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:55:21,222 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:55:21,222 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:55:21,223 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-08-01 01:55:21,226 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:55:21,226 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:55:21,226 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:55:21,226 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:55:21,226 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-08-01 01:55:21,228 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:55:21,228 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:55:21,228 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:55:21,228 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:55:21,228 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-08-01 01:55:21,235 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:55:21,235 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:55:21,235 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:55:21,235 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:55:21,235 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-08-01 01:55:21,238 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:55:21,238 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:55:21,238 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:55:21,238 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:55:21,238 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-08-01 01:55:21,240 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:55:21,240 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:55:21,240 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:55:21,240 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:55:21,240 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-08-01 01:55:21,243 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:55:21,243 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:55:21,243 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:55:21,243 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:55:21,244 5588 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-08-01 01:55:21,298 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:21] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 2 0.004 0.949
2025-08-01 01:55:21,304 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:21] "GET /web/webclient/load_menus/1fbb8d53ea109792f3329087f8a3fd1a84012a05b7be4ff04ed2939ee08b642a HTTP/1.1" 200 - 2 0.007 0.731
2025-08-01 01:55:21,308 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:21] "GET /web/webclient/translations/884607bda48db46d5087c1dcec9ccb31b1799244?lang=zh_CN HTTP/1.1" 200 - 3 0.009 0.728
2025-08-01 01:55:22,038 5588 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/e04d3ff/web.assets_web.min.css (id:204) 
2025-08-01 01:55:22,040 5588 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [202] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/e04d3ff/web.assets_web.min.css 
2025-08-01 01:55:22,079 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:22] "GET /web/assets/e04d3ff/web.assets_web.min.css HTTP/1.1" 200 - 17 0.136 1.595
2025-08-01 01:55:22,792 5588 INFO odoo odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/b340998/web.assets_web.min.js (id:205) 
2025-08-01 01:55:22,793 5588 INFO odoo odoo.addons.base.models.assetsbundle: Deleting attachments [203] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/b340998/web.assets_web.min.js 
2025-08-01 01:55:22,809 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:22] "GET /web/assets/b340998/web.assets_web.min.js HTTP/1.1" 200 - 13 0.022 2.438
2025-08-01 01:55:23,346 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.005 0.007
2025-08-01 01:55:23,350 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/action/load HTTP/1.1" 200 - 8 0.011 0.010
2025-08-01 01:55:23,678 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.012
2025-08-01 01:55:23,680 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.019 0.028
2025-08-01 01:55:23,706 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.007 0.005
2025-08-01 01:55:23,757 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard/get_views HTTP/1.1" 200 - 25 0.071 0.040
2025-08-01 01:55:23,785 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard.theme/search_read HTTP/1.1" 200 - 3 0.005 0.009
2025-08-01 01:55:23,793 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard/get_user_groups HTTP/1.1" 200 - 6 0.013 0.008
2025-08-01 01:55:23,807 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard/search_read HTTP/1.1" 200 - 12 0.017 0.018
2025-08-01 01:55:23,820 5588 WARNING odoo odoo.osv.expression: The domain term '('dashboard_id', '=', [1])' should use the 'in' or 'not in' operator. 
2025-08-01 01:55:23,824 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard.filter/fetch_by_dashboard HTTP/1.1" 200 - 3 0.003 0.005
2025-08-01 01:55:23,835 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.dashboard.block/search_read HTTP/1.1" 200 - 6 0.014 0.011
2025-08-01 01:55:23,854 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:55:23,866 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:55:23,909 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 14 0.043 0.021
2025-08-01 01:55:23,909 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 14 0.050 0.015
2025-08-01 01:55:23,996 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 40 0.111 0.039
2025-08-01 01:55:23,998 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:23] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 42 0.099 0.051
2025-08-01 01:55:24,002 5588 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-01 01:55:25,965 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:25] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.005
2025-08-01 01:55:49,105 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/get_views HTTP/1.1" 200 - 17 0.011 0.031
2025-08-01 01:55:49,133 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 3 0.005 0.009
2025-08-01 01:55:49,147 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.010 0.008
2025-08-01 01:55:49,148 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.010 0.011
2025-08-01 01:55:49,188 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:55:49,212 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.010 0.049
2025-08-01 01:55:49,221 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 11 0.026 0.040
2025-08-01 01:55:49,239 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 18 0.031 0.051
2025-08-01 01:55:49,242 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 18 0.031 0.053
2025-08-01 01:55:49,243 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 23 0.050 0.039
2025-08-01 01:55:49,249 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.049 0.045
2025-08-01 01:55:49,251 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.023 0.013
2025-08-01 01:55:49,251 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:49] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.014 0.011
2025-08-01 01:55:56,024 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:56] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 18 0.007 0.018
2025-08-01 01:55:56,033 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:55:56,088 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:55:56] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.025 0.034
2025-08-01 01:56:00,626 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:00] "POST /web/dataset/call_kw/izi.analysis/save_analysis_visual_config HTTP/1.1" 200 - 16 0.014 0.019
2025-08-01 01:56:00,640 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:56:00,700 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:00] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.029 0.038
2025-08-01 01:56:02,441 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:56:02,476 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:02] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.015 0.024
2025-08-01 01:56:57,247 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.visual.type/search_read HTTP/1.1" 200 - 2 0.021 0.033
2025-08-01 01:56:57,251 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_languanges HTTP/1.1" 200 - 3 0.014 0.032
2025-08-01 01:56:57,252 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/search_read HTTP/1.1" 200 - 4 0.011 0.042
2025-08-01 01:56:57,267 5588 WARNING odoo odoo.http: <function odoo.addons.web.controllers.dataset.call_kw> called ignoring args {'context'} 
2025-08-01 01:56:57,281 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_filter_info HTTP/1.1" 200 - 6 0.010 0.013
2025-08-01 01:56:57,286 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.visual.type/get_visual_config HTTP/1.1" 200 - 8 0.013 0.014
2025-08-01 01:56:57,302 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.023 0.021
2025-08-01 01:56:57,307 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.024 0.021
2025-08-01 01:56:57,308 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.027 0.020
2025-08-01 01:56:57,315 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.012 0.014
2025-08-01 01:56:57,315 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/ui_get_analysis_info HTTP/1.1" 200 - 17 0.013 0.017
2025-08-01 01:56:57,353 5588 INFO odoo werkzeug: 127.0.0.1 - - [01/Aug/2025 01:56:57] "POST /web/dataset/call_kw/izi.analysis/try_get_analysis_data_dashboard HTTP/1.1" 200 - 27 0.065 0.030
2025-08-01 02:18:05,243 8565 INFO ? odoo: Odoo version 17.0 
2025-08-01 02:18:05,243 8565 INFO ? odoo: Using configuration file at /Users/<USER>/MelGeek/odoo-17.0/odoo.conf 
2025-08-01 02:18:05,243 8565 INFO ? odoo: addons paths: ['/Users/<USER>/MelGeek/odoo-17.0/odoo/addons', '/Users/<USER>/Library/Application Support/Odoo/addons/17.0', '/Users/<USER>/MelGeek/odoo-17.0/addons', '/Users/<USER>/MelGeek/hex-erp/buy-addons'] 
2025-08-01 02:18:05,243 8565 INFO ? odoo: database: odoo@localhost:5432 
2025-08-01 02:18:05,323 8565 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-08-01 02:18:05,475 8565 INFO ? odoo.service.server: HTTP service (werkzeug) running on *********.in-addr.arpa:8069 

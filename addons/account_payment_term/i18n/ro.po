# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_term
# 
# Translators:
# <PERSON><PERSON><PERSON> <fold<PERSON><PERSON>@nexterp.ro>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>risa_nexterp, 2025
# Larisa Pop, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-13 08:17+0000\n"
"PO-Revision-Date: 2024-06-22 22:00+0000\n"
"Last-Translator: Larisa Pop, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_payment_term
#: model:account.payment.term,name:account_payment_term.account_payment_term_advance
msgid "90 days, on the 10th"
msgstr "90 de zile, pe data de 10"

#. module: account_payment_term
#: model:ir.model.fields.selection,name:account_payment_term.selection__account_payment_term_line__delay_type__days_end_of_month_on_the
msgid "Days end of month on the"
msgstr "Zile final de lună pe"

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__days_next_month
msgid "Days on the next month"
msgstr "Ziua din luna următoare"

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__delay_type
msgid "Delay Type"
msgstr "Tip intarziere"

#. module: account_payment_term
#: model:ir.model.fields,field_description:account_payment_term.field_account_payment_term_line__display_days_next_month
msgid "Display Days Next Month"
msgstr "Afișează zilele din luna următoare"

#. module: account_payment_term
#: model:ir.model,name:account_payment_term.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Linie Termen de Plată"

#. module: account_payment_term
#: model_terms:account.payment.term,note:account_payment_term.account_payment_term_advance
msgid "Payment terms: 90 days, on the 10th"
msgstr "Termen de plată: 90 zile, pe data de 10"

#. module: account_payment_term
#. odoo-python
#: code:addons/account_payment_term/models/account_payment_term.py:0
#, python-format
msgid "The days added must be a number and has to be between 0 and 31."
msgstr "Numărul de zile adaugate trebuie sa fie un număr între 0 și 31"

#. module: account_payment_term
#. odoo-python
#: code:addons/account_payment_term/models/account_payment_term.py:0
#, python-format
msgid "The days added must be between 0 and 31."
msgstr "Numărul de zile adaugate trebuie sa fie intre 0 și 31"

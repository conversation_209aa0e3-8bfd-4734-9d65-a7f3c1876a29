# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_peppol
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"<br/>\n"
"                                In Belgium, electronic invoicing is <u>mandatory as of January 2026</u> - don't wait to register."
msgstr ""
"<br/>\n"
"                                比利時將<u>由 2026 年 1 月起強制使用</u>電子發票。建議你不要等待，立即註冊。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid ""
"<span class=\"mx-1\" invisible=\"not peppol_is_demo_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        The invoice will be sent automatically to PEPPOL\n"
"                    </span>"
msgstr ""
"<span class=\"mx-1\" invisible=\"not peppol_is_demo_uuid\">(演示)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        發票將自動發送至 PEPPOL\n"
"                    </span>"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                    Peppol Details\n"
"                                </span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\"/>"
msgstr ""
"<span class=\"o_form_label\">\n"
"                                    Peppol 詳情\n"
"                                </span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"此處設定的值是特定於每間公司的。\"/>"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'demo'\"> (Demo)</span>\n"
"                                        <span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'test'\"> (Test)</span>"
msgstr ""
"<span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'demo'\"> (示範)</span>\n"
"                                        <span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'test'\"> (測試)</span>"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span>\n"
"                                    I want to migrate my Peppol connection to Odoo (optional):\n"
"                                </span>"
msgstr ""
"<span>\n"
"                                    我想將 Peppol 連接遷移到 Odoo（可選）：\n"
"                                </span>"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"A participant with these details has already been registered on the network."
" If you have previously registered to a Peppol service, please deregister."
msgstr "Peppol 網絡上已有參與者使用相同的資料註冊。如果你之前已註冊使用 Peppol 服務，請取消註冊。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "A purchase journal must be used to receive Peppol documents."
msgstr "接收 Peppol 文件必須使用採購日記賬。"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "賬戶 EDI 代理使用者"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send
msgid "Account Move Send"
msgstr "賬戶分錄傳送"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_mode
msgid "Account Peppol Edi Mode"
msgstr "賬戶 Peppol Edi 模式"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__account_peppol_edi_mode_info
msgid "Account Peppol Edi Mode Info"
msgstr "賬戶 Peppol Edi 模式訊息"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_mode_constraint
msgid "Account Peppol Mode Constraint"
msgstr "賬戶 Peppol 模式限制"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__is_peppol_journal
msgid "Account used for Peppol"
msgstr "用於 Peppol 的賬戶"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__active
msgid "Active"
msgstr "啟用"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Application status:"
msgstr "應用程式狀態:"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"By clicking the button below I accept that Odoo may process my e-invoices."
msgstr "點擊以下按鈕即表示我同意 Odoo 處理我的電子發票。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"Can't cancel an active registration. Please request a migration or "
"deregister instead."
msgstr "無法取消活動註冊。請改為申請遷移。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Can't cancel registration with this status: %s"
msgstr "無法取消此狀態的註冊: %s"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Can't deregister with this status: %s"
msgstr "無法以這種狀態取消註冊: %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid "Cancel PEPPOL"
msgstr "取消 Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Cancel registration"
msgstr "取消註冊"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__canceled
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__canceled
msgid "Canceled"
msgstr "已取消"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move.py:0
#, python-format
msgid "Cannot cancel an entry that has already been sent to PEPPOL"
msgstr "無法取消已傳送至 Peppol 的記項"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_valid_format
msgid "Cannot receive this format"
msgstr "未能接收此格式"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Cannot register a user with a %s application"
msgstr "無法使用 %s 應用程式註冊用戶"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_validity_last_check
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_validity_last_check
msgid "Checked on"
msgstr "上次檢查："

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"用於識別 BIS 賬單 3.0 及其衍生產品所使用終端點的代碼。\n"
"             詳細列表： https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_company
msgid "Companies"
msgstr "公司"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Confirm"
msgstr "確認"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Connection error, please try again later."
msgstr "連線錯誤，請稍後再試。"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Contact details were updated."
msgstr "聯絡資料已更新。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Contact email and mobile number are required."
msgstr "必須填寫聯絡電郵及流動電話號碼。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__demo
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__demo
#, python-format
msgid "Demo"
msgstr "示範"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Deregister from Peppol"
msgstr "從 Peppol 取消註冊"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__done
msgid "Done"
msgstr "完成"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_user
msgid "EDI user"
msgstr "EDI 使用者"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "Edi Identification"
msgstr "Edi 標識"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__enable_peppol
msgid "Enable Peppol"
msgstr "啟用 Peppol"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__error
msgid "Error"
msgstr "錯誤"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch Peppol invoice status"
msgstr "獲取 Peppol 發票狀態"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch from Peppol"
msgstr "從 Peppol 獲取"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"In demo mode sending and receiving invoices is simulated. There will be no "
"communication with the Peppol network."
msgstr "在演示模式下，將模擬發送和接收發票。不會與 Peppol 網絡通訊。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr "收到發票日記賬"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__is_peppol_edi_format
#: model:ir.model.fields,field_description:account_peppol.field_res_users__is_peppol_edi_format
msgid "Is Peppol Edi Format"
msgstr "是 Peppol Edi 格式"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_journal
msgid "Journal"
msgstr "日記賬"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_partner__account_peppol_validity_last_check
#: model:ir.model.fields,help:account_peppol.field_res_users__account_peppol_validity_last_check
msgid "Last Peppol endpoint verification"
msgstr "上次 Peppol 終端點驗證"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__prod
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__prod
msgid "Live"
msgstr "實時"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_migration_key
msgid "Migration Key"
msgstr "遷移密鑰"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Migration key"
msgstr "遷移密鑰"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Mobile Number"
msgstr "流動電話號碼"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "Mobile number (for validation)"
msgstr "流動電話號碼（用作驗證）"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_registered
msgid "Not registered"
msgstr "未設立"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_valid
msgid "Not valid"
msgstr "無效"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_verified
msgid "Not verified"
msgstr "未驗證"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_verified
msgid "Not verified yet"
msgstr "尚未驗證"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "Odoo"
msgstr "Odoo"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_edi_proxy_client_user__proxy_type__peppol
msgid "PEPPOL"
msgstr "Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__is_account_peppol_participant
msgid "PEPPOL Participant"
msgstr "Peppol 參與者"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_purchase_journal_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_purchase_journal_id
msgid "PEPPOL Purchase Journal"
msgstr "Peppol 採購日記賬"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_is_endpoint_valid
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_is_endpoint_valid
msgid "PEPPOL endpoint validity"
msgstr "Peppol 終端點有效性"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_message_uuid
msgid "PEPPOL message ID"
msgstr "Peppol 訊息識別碼"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_proxy_state
msgid "PEPPOL status"
msgstr "Peppol 狀態"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_new_documents_ir_actions_server
msgid "PEPPOL: retrieve new documents"
msgstr "Peppol：讀取新文件"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_message_status_ir_actions_server
msgid "PEPPOL: update message status"
msgstr "Peppol：更新訊息狀態"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_participant_status_ir_actions_server
msgid "PEPPOL: update participant status"
msgstr "Peppol：更新參與者狀態"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__pending
msgid "Pending"
msgstr "等待"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__processing
msgid "Pending Reception"
msgstr "待接待"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol EAS"
msgstr "Peppol EAS"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol Endpoint"
msgstr "Peppol 終端點"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_is_demo_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_is_demo_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_is_demo_uuid
msgid "Peppol Is Demo Uuid"
msgstr "Peppol 是示範識別碼"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol Ready"
msgstr "Peppol 準備就緒"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_journal.py:0
#, python-format
msgid "Peppol Ready invoices"
msgstr "Peppol 準備好的發票"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol Validity"
msgstr "Peppol 有效性"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol document has been received successfully"
msgstr "成功接收 Peppol 文件"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol 電子地址（EAS）"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_verification_label
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_verification_label
msgid "Peppol endpoint validity"
msgstr "Peppol 終端點有效性"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol error: %s"
msgstr "Peppol 錯誤： %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Peppol ready invoices"
msgstr "Peppol 現成發票"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol status"
msgstr "Peppol 狀態"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol status update: %s"
msgstr "Peppol 狀態更新： %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Please do not hesitate to contact our support if you need further "
"assistance."
msgstr "如果需要進一步幫助，請隨時聯絡我們的支援人員。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Please enter a mobile number to verify your application."
msgstr "請輸入流動電話號碼，以驗證你的申請。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Please enter a primary contact email to verify your application."
msgstr "請輸入主要聯絡人的電子郵件，以驗證您的申請。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid ""
"Please enter the mobile number in the correct international format.\n"
"For example: +***********, where +32 is the country code.\n"
"Currently, only European countries are supported."
msgstr ""
"請以正確的國際格式輸入流動電話號碼。\n"
"例如：+***********，其中 +32 是國家/地區代碼。\n"
"目前只支援歐洲國家。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Please fill in the EAS code and the Participant ID code."
msgstr "請填寫 EAS代碼及參與者 ID代碼。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "Please install the phonenumbers library."
msgstr "請安裝電話號碼程式庫。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "Please verify partner configuration in partner settings."
msgstr "請在合作夥伴設定中，驗證合作夥伴配置。"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_contact_email
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Primary contact email"
msgstr "主要聯絡電郵"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_contact_email
msgid "Primary contact email for Peppol-related communication"
msgstr "Peppol 相關通訊的主要聯絡電郵"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "代理類型"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__to_send
msgid "Queued"
msgstr "已加入隊列"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__ready
msgid "Ready to send"
msgstr "準備發送"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__is_account_peppol_participant
msgid "Register as a PEPPOL user"
msgstr "註冊為 Peppol 使用者"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__rejected
msgid "Rejected"
msgstr "已拒絕"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__peppol_verification_code
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_verification_code
msgid "SMS verification code"
msgstr "短訊驗證碼"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Send again"
msgstr "再次傳送"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_account_move_send__checkbox_send_peppol
msgid "Send the invoice via PEPPOL"
msgstr "透過 Peppol 傳送發票"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__checkbox_send_peppol
msgid "Send via PEPPOL"
msgstr "透過 Peppol 傳送"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__skipped
msgid "Skipped"
msgstr "忽略"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Start sending and receiving documents via Peppol as soon as your "
"registration is complete."
msgstr "註冊完成後，即可通過 Peppol 發送和接收文件。"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Switch to Live"
msgstr "切換至實時"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__test
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__test
#, python-format
msgid "Test"
msgstr "測試"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Test mode allows registration of the user on the test Peppol network.\n"
"                                        By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""
"測試模式允許用戶在測試 Peppol 網絡上註冊。\n"
"                                        通過點擊下面的按鈕，我同意 Odoo 可以處理我的電子發票。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "The Peppol endpoint identification number is not correct."
msgstr "Peppol 終端點識別號碼不正確。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The Peppol service that is used is likely to be %s."
msgstr "所使用的 Peppol 服務很可能是 %s。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "The document has been sent to the Peppol Access Point for processing"
msgstr "文件已發送至 Peppol 接入點進行處理"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"The endpoint number might not be correct. Please check if you entered the "
"right identification number."
msgstr "終端點號碼可能不正確。 請檢查你輸入的身份證號碼是否正確。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid ""
"The following partners are not correctly configured to receive Peppol "
"documents. Please check and verify their Peppol endpoint and the Electronic "
"Invoicing format: %s"
msgstr "以下合作夥伴的設定不正確，未能接收 Peppol 文件。請檢查及驗證其 Peppol 終端點以及電子發票格式： %s"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "The partner is missing Peppol EAS and/or Endpoint identifier."
msgstr "合作夥伴未有 Peppol EAS 及/或終端點識別資料。"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_partner__account_peppol_is_endpoint_valid
#: model:ir.model.fields,help:account_peppol.field_res_users__account_peppol_is_endpoint_valid
msgid "The partner's EAS code and PEPPOL endpoint are valid"
msgstr "合作夥伴的 EAS 代碼及 Peppol 終端點有效"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
#, python-format
msgid ""
"The peppol status of the documents has been reset when switching from Demo "
"to Live."
msgstr "從演示版切換到實時版時，文件的 peppol 狀態已重置。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"The recommended EAS code for Belgium is 0208. The Endpoint should be the "
"Company Registry number."
msgstr "推薦的比利時 EAS 代碼是 0208。終端點應該是公司註冊號碼"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "The rejection reason has been sent to you via email."
msgstr "拒絕原因已通過電子郵件發送給你。"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr "識別此用戶的唯一識別碼，通常是增值稅(VAT)編號"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The verification code is not correct"
msgstr "驗證碼不正確"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The verification code should contain six digits."
msgstr "驗證碼應包含六位數字。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"This feature is deprecated. Contact odoo support if you need a migration "
"key."
msgstr "此功能已棄用。若需要遷移金鑰，請聯絡 Odoo 技術支援。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "This invoice has also been"
msgstr "此發票也已經"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "This verification code has expired. Please request a new one."
msgstr "此驗證碼已過期。請索取全新一個。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"To generate complete electronic invoices, also set a country for this "
"partner."
msgstr "要產生完整的電子發票，請亦為此合作夥伴設定國家/地區。"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Too many attempts to request an SMS code. Please try again later."
msgstr "要求簡訊代碼的嘗試次數太多。請稍後再試。"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr "BIS 賬單 3.0 及其衍生產品所使用的唯一標識符，又稱為「終端點識別碼」。"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Update contact details"
msgstr "更新聯絡資料"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__is_account_peppol_participant
msgid "Use PEPPOL"
msgstr "使用 Peppol"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_mode_constraint
msgid ""
"Using the config params, this field specifies which edi modes may be "
"selected from the UI"
msgstr "通過配置參數，該欄位可指定可從用戶界面選擇哪些 edi 模式"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__valid
msgid "Valid"
msgstr "有效"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration"
msgstr "驗證註冊"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration (Demo)"
msgstr "驗證註冊（示範）"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration (Test)"
msgstr "驗證註冊（測試）"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__sent_verification
msgid "Verification code sent"
msgstr "驗證碼已發送"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify"
msgstr "驗證"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.partner_action_verify_peppol
msgid "Verify Peppol"
msgstr "驗證 Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Verify mobile number"
msgstr "驗證流動電話號碼"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify partner's PEPPOL endpoint"
msgstr "驗證合作夥伴的 Peppol 終端點"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__peppol_warning
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint_warning
msgid "Warning"
msgstr "警告"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "We sent a verification code to"
msgstr "剛發送驗證碼至"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid ""
"You will not be able to send or receive Peppol documents in Odoo anymore. "
"Are you sure you want to proceed?"
msgstr "你將無法再在 Odoo 中發送或接收 Peppol 文件。 確定要繼續？"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "You will receive a verification code to this mobile number"
msgstr "你將收到發送至此流動電話號碼的驗證碼"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your Peppol identification is:"
msgstr "你的 Peppol 身份識別是："

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Your confirmation code is"
msgstr "你的確認碼是"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your migration key is:"
msgstr "你的遷移密鑰是："

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your registration should be activated within a day."
msgstr "你的註冊應該會在一天內啟動。"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "could not be sent via Peppol"
msgstr "未能透過 Peppol 傳送"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "sent via Peppol"
msgstr "已透過 Peppol 傳送"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "to send invoices, but this one"
msgstr "以傳送發票，但這個"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "uses"
msgstr "使用"

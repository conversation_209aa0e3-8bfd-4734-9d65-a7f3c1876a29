<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="0">
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_equal">
      <field name="name">=</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_not_equal_1">
      <field name="name">!=</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_equal_2">
      <field name="name">&lt;&gt;</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_greater_than">
      <field name="name">&gt;</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_equal_greater_than_or_equal">
      <field name="name">&gt;=</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_less_than">
      <field name="name">&lt;</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_less_than_or_equal">
      <field name="name">&lt;=</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_like">
      <field name="name">like</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_not_like">
      <field name="name">not like</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_ilike">
      <field name="name">ilike</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_not_ilike">
      <field name="name">not ilike</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_is">
      <field name="name">is</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_is_not">
      <field name="name">is not</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_in">
      <field name="name">in</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.analysis.filter.operator" id="izi_data.izi_analysis_filter_operator_db_odoo_not_in">
      <field name="name">not in</field>
      <field name="source_type">db_odoo</field>
    </record>
  </data>
</odoo>



<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="0">
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_1">
      <field name="name">boolean</field>
      <field name="type_mapping">boolean</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_2">
      <field name="name">bytea</field>
      <field name="type_mapping">byte</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_3">
      <field name="name">date</field>
      <field name="type_mapping">date</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_4">
      <field name="name">float8</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_5">
      <field name="name">int4</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_6">
      <field name="name">int8</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_7">
      <field name="name">text</field>
      <field name="type_mapping">string</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_8">
      <field name="name">timestamp</field>
      <field name="type_mapping">datetime</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_9">
      <field name="name">numeric</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_10">
      <field name="name">varchar</field>
      <field name="type_mapping">string</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_11">
      <field name="name">timestamptz</field>
      <field name="type_mapping">timestamp</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_12">
      <field name="name">bit</field>
      <field name="type_mapping">byte</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_13">
      <field name="name">bpchar</field>
      <field name="type_mapping">string</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_14">
      <field name="name">c</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_15">
      <field name="name">jsonb</field>
      <field name="type_mapping">string</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_16">
      <field name="name">float4</field>
      <field name="type_mapping">number</field>
      <field name="source_type">db_odoo</field>
    </record>
    <record model="izi.table.field.mapping" id="izi_data.izi_table_field_mapping_db_odoo_16">
      <field name="name">bool</field>
      <field name="type_mapping">boolean</field>
      <field name="source_type">db_odoo</field>
    </record>
  </data>
</odoo>



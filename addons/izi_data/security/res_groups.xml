<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="0">
    <record model="ir.module.category" id="module_category_izi_analysis">
      <field name="name">IZI Data Analysis</field>
      <field name="description">Helps you handle your analysis.</field>
      <field name="sequence">210</field>
    </record>

    <record id="group_user_analysis" model="res.groups">
      <field name="name">User: Own Data Analysis Only</field>
      <field name="category_id" ref="izi_data.module_category_izi_analysis"/>
      <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
      <field name="comment">the user will have access to his own dashboard in the IZI Analysis Dashboard application.</field>
    </record>

    <record id="group_manager_analysis" model="res.groups">
      <field name="name">Manager: All Data Analysis Dashboard</field>
      <field name="category_id" ref="izi_data.module_category_izi_analysis"/>
      <field name="implied_ids" eval="[(4, ref('group_user_analysis'))]"/>
      <field name="comment">the user will have access to all analysis dashboard of everyone in the IZI Analysis Dashboard application.</field>
      <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
      <field name="groups_id" eval="[(4,ref('izi_data.group_manager_analysis'))]"/>
    </record>

    <record id="izi_analysis_rule" model="ir.rule">
          <field name="name">Analysis</field>
          <field name="model_id" ref="model_izi_analysis"/>
          <field name="domain_force">['|', ('group_ids', '=', False), ('group_ids', 'in', user.groups_id.ids)]</field>
    </record>
  </data>
</odoo>
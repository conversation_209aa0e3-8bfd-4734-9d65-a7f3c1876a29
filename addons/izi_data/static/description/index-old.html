<div>
    <!-- Intro -->
    <section class="oe_container" style="padding-top: 1px;">
        <div>
            <div style="display: block;text-align: center;margin-top: 50px;">
                <img class="img-border img-responsive thumbnail" style="max-width: 500px" src="images/izi_and_odoo_ready.png"/>
            </div>
            <div class="text-center text-muted" style="font-weight: 400; margin-bottom: 8px;">
                <span class="o_gradient" style="width: 100%; height: 2px; display: inline-block; border-radius: 3px;"> 
                </span>
            </div>
            <div>
                <h3 class="oe_slogan" style="font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif; font-size: 40px; color: #030303; line-height: 2em; opacity:unset;margin-bottom: 0px;"><strong>IZI Analytic Data Query</strong></h3>
            </div>
            <h3 class="oe_slogan" style="margin: auto;width: 80%; font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif; opacity: 1;line-height: 1.5;font-size: 20px; color: #FF0000; margin-top: 0px; text-align: justify;">
                <b>
                    This module is a dependency for IZI Analytic Dashboard module.
                    You cannot use this module separately from the main module.
                    You can check the main module from this link.
                    <a href="https://apps.odoo.com/apps/modules/14.0/izi_dashboard">IZI Analytic Dashboard</a>
                </b>
            </h3>
            <div>
                <h2 class="oe_slogan" style="font-size: 32px; color: #030303; line-height: 2em; margin-bottom: 0px;">Try at <b>demo.iziapp.id</b></h2>
            </div>
            <a href="https://demo.iziapp.id" target="_blank" style="font-size: 20px; font-weight: bold; display: block; width: 300px;text-align: center;margin: auto; margin-bottom: 20px; color: white; font-family: lato; padding: 15px 20px;  background-color: #483fc9; align-content: normal; border-radius: 5px; cursor: pointer;" class="s_figure_link">
                demo / demo
            </a>
            <div class="text-center mb-5" style="display: block; width: 400px; margin: auto;">
                <a href="mailto:<EMAIL>" target="_blank"
                style="float: left; width: 48%; margin: 0 1%; height: 83px; color:#091E42; font-family:Montserrat; display:block; padding:13px 30px; border:1px solid #050505; border-radius:10px" class="mx-1 mb-2 deep-1 deep_hover">
                    <img style="max-width: 67px;" class="img img-fluid"
                        src="images/email.png"/>
                    <b style="display: block;color: rgb(8, 8, 8);">Email Us</b>
                </a>
                <a href="mailto:<EMAIL>" target="_blank"
                style="float: left; width: 48%; margin: 0 1%; height: 83px; color:#091E42; font-family:Montserrat; display:block; padding:13px 30px; border:1px solid #050505; border-radius:10px" class="mx-1 mb-2 deep-1 deep_hover">
                    <img style="max-width: 67px;" class="img img-fluid"
                        src="images/chat_with_us.png"/>
                    <b style="display: block;color: rgb(8, 8, 8);">Chat with Us</b>
                </a>
                <div style="clear:both;">&nbsp;</div>
            </div>
            <div style="clear:both;">&nbsp;</div>
            <div style="margin: auto;display: block;text-align: center;">
                <a href="https://www.youtube.com/channel/UCU7MO8xOvnWRPSP60uVyObQ/playlists" target="_blank" style="color:#091E42; font-family:Montserrat; display:inline-block; padding:13px 30px; border:1px solid #050505; border-radius:10px" class="mx-1 mb-2 deep-1 deep_hover">
                    <span class="fa fa-youtube-play mr-2" style="color:#f50303; font-size:20px; vertical-align:middle"></span>
                    <span style="color:#0f1e40; font-size:16px; vertical-align:middle">Watch Demo</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Highlight Feature -->
    <div style="border-radius: 7px;border: 1px solid #e4e4e4 !important; background-image: linear-gradient(180deg, rgb(255, 255, 255) 37%, rgb(254, 255, 255) 100%);"
        class="row mb-3 position-relative overflow-hidden mt-4 pb-5">
        <div class="col-12">
            <div>
                <h1 class="text-center my-3 pt-4 pb-2" style="color: #34495e;">Features</h1>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_ui.png"/>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">User Interface</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        The analytic view follow the common UI used by the best analytic platforms in the world.
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_clean.png"/>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Clean and Modern Chart</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Using free version of amChart, a beautiful, modern chart libary used by big companies.
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_query.png"/>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Query Editor</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Editor to input query directly to database tables. We can get the data we need effectively and efficiently.
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_metric.png"/>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Metrics and Dimensions</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Easily set the metrics and dimensions of our analysis by clicking all available fields on the left panel.
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_template.png"/>
                    <b style="font-family: lato; color: #ffff; font-size:14px;">
                        <span style="background-color: #875A7B; border-color: #875A7B; font-weight: 300;" class="badge badge-gamma ml-1 float-right"> 
                            <i class="fa fa-lock" style="padding-right: 3px;"> </i>Free Sales Analysis Template to Download.
                        </span>
                    </b>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Ready Analysis Template</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Ready analysis templates will be available for non technical users.
                        <a href="https://apps.odoo.com/apps/modules/14.0/izi_data_template_sale/">Download Free Sales Analysis Template</a>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_external.png"/>
                    <b style="font-family: lato; color: #ffff; font-size:14px;">
                        <span style="background-color: #875A7B; border-color: #875A7B; font-weight: 300;" class="badge badge-gamma ml-1 float-right"> 
                            <i class="fa fa-lock" style="padding-right: 3px;"> </i>Will Be Available On Separate Modules.
                        </span>
                    </b>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Connect to External Data Sources</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Data source connectors are available to access external databases, files, and APIs.
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white; border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_filter.png"/>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Dynamic Filters</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Filters can be dynamically added in analysis to make data exploration easier.
                    </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 my-2">
            <div style="background-color: white;border-radius: 3px;" class="card deep-2 deep_hover p-3 h-100">
                <div style="margin-left: 5%;">
                    <img style="width: 80px; height: 80px;" src="icons/ic_customize.png"/>
                    <b style="font-family: lato; color: #ffff; font-size:14px;">
                        <span style="background-color: #875A7B; border-color: #875A7B; font-weight: 300;" class="badge badge-gamma ml-1 float-right"> 
                            <i class="fa fa-lock" style="padding-right: 3px;"> </i>Limited Themes in This Module.
                        </span>
                    </b>
                </div>
                <div class="card-body">
                    <b style="font-family: lato; color: #34495e;font-size:14px;">Dashboard Customization</b>
                    <br>
                    <span class="text-500" style="color: #55626f !important; font-family: 'Lato-Lig';">
                        Dashboard layout can be configured easily and there are several options for dashboard themes.
                    </span>
                </div>
            </div>
        </div>
    </div>


    <!-- Feature, Screenshot, FAQs -->
    <div class="row mt32 mb32" style="border:1px solid #eee; border-radius:10px; display: block; height: auto;">
        <h1 style="text-align: center;width: 100%;display: block;margin: 10px auto;">Screenshots</h1>
        
        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_select_analysis.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We don't just create dashboard right away without analyzing the data, do we?
                    <br/><br/>
                    In IZI Analytic Platform, we start by clicking the Analysis menu and 
                    create an analysis from queries or available templates. 
                    <br/><br/>
                    We provide some analysis templates for 
                    you to explore. You can just click the analysis and boom! the 
                    chart will appear and your exploration begin! 
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_analysis.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    Analysis is an object that represent our exploration of specific data from queries.
                    <br/><br/>
                    We can then select metrics, dimensions and filters to explore our data furthermore.
                    <br/><br/>
                    Metrics are the quantitative measurements of data and dimensions are the labels used to describe them.
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_metric_dimension.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    All the fields from the analysis table will appear on the left panel.
                    <br/><br/>
                    We can easily setup our metrics and dimensions by clicking on them.
                    <br/><br/>
                    We can also set the order (ascending or descending), the calculation applied for the metrics,
                    and the format for each dimensions. 
                    <br/><br/>
                    All in one beautiful UI.
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_filter_analysis.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can add a dynamic filter by selecting the field and the values on the top right of the chart.
                    <br/><br/>

                    <br/><br/>

                    <br/><br/>

                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_visual.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can cange the visual setting of an analysis by clicking the Visual tab.
                    <br/><br/>
                    There are several visual types available and will be added many more later.
                    <br/><br/>
                    We can also configure detail visual settings like the radius of a pie chart, the position of the legends, etc.
                    <br/><br/>
                    
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_zoom.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can zoom the bar and line chart to see specific data in details.
                    <br/><br/>
                    
                    <br/><br/>
                    
                    <br/><br/>
                    
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_query.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    If we want to modify the data query, we can edit the analysis and input the new query there.
                    <br/><br/>
                    We can click on the debug icon button to test the query and process the query to rebuild our analysis table and fields.
                    <br/><br/>

                    <br/><br/>
                    
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_add_dashboard.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    After we finish exploring the data and choosing the right visual configuration, we can finally add our analysis to the dashboard.
                    <br/><br/>
                    Select the dashboard and click Add to Dashboard.
                    <br/><br/>

                    <br/><br/>
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_dashboard.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can go to the Dashboard menu and select an existing dashboard or create a new one.
                    <br/><br/>
                    Afterthat, the system will render all our analysis that has been added to the dashboard.
                    <br/><br/>
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 50%;" src="images/sc_dashboard_filter.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can still play with our filter here in the dashboard view.
                    <br/><br/>
                    We can also remove an analysis from the dashboard.
                    <br/><br/>
                    Or go back to our analysis view to edit the analysis.
                    <br/><br/>
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_theme.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    We can change the layout and select a theme to customize the dashboard.
                    <br/><br/>

                    <br/><br/>

                    <br/><br/>
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>

        <div style="margin: 10px 0px; padding: 16px; display: block; width: 100%;">
            <div style="width: 70%; float: left; display: block; height: auto; text-align: right;">
                <img style="height: auto; width: 100%;" src="images/sc_external_db.png"/>
            </div>
            <div style="width: 30%; float: left; display: block; height: 400px; text-align: left; padding: 0px 20px;">
                <h2 style="font-family: 'Lato-Lig'; font-size: 15px;font-weight: 400;line-height: 1.5;">
                    IZI Analytic Platform is able to extract data from various data sources, not only Odoo.
                    <br/><br/>
                    Currently, we have data source connectors to External PostgreSQL and MySQL Databases.
                    <br/><br/>
                    We will add more connectors to access other type of databases, data files and external application APIs.
                    <br/><br/>
                </h2>
            </div>
            <div style="clear:both"></div>
        </div>


        <div style="clear:both"></div>
    </div>

</div>
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record model="ir.actions.act_window" id="izi_data_source_action">
            <field name="name">Data Source</field>
            <field name="res_model">izi.data.source</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'active_test':False}</field>
        </record>

        <record model="ir.actions.act_window" id="izi_table_action">
            <field name="name">Table</field>
            <field name="res_model">izi.table</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_izi_table_filter"/>
            <field name="context">{'search_default_filter_user_defined': 1}</field>
        </record>

        <record model="ir.actions.act_window" id="izi_analysis_action">
            <field name="name">Analysis</field>
            <field name="res_model">izi.analysis</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0), 
            (0, 0, {'view_mode': 'tree'}), 
            (0, 0, {'view_mode': 'form', 'view_id': ref('izi_data.izi_analysis_form_without_footer')})]"/>

        </record>
    </data>
</odoo>
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="izi_analysis" model="ir.ui.view">
            <field name="name">izi.analysis</field>
            <field name="model">izi.analysis</field>
            <field name="type">izianalysis</field>
            <field name="arch" type="xml">
                <izianalysis string="Analysis">
                </izianalysis>
            </field>
        </record>

        <record model="ir.ui.view" id="izi_dashboard.izi_analysis_form">
            <field name="name">izi.analysis.form</field>
            <field name="model">izi.analysis</field>
            <field name="inherit_id" ref="izi_data.izi_analysis_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='left-side']" position="inside">
                    <field invisible="1" name="visual_type_id" required="1" placeholder="Choose the type of the charts" widget="selection" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                    <field invisible="1" name="metric_field_ids" placeholder="Choose the quantitative fields to measure" domain="[('table_id', '=', table_id), ('field_type', 'in', ('numeric', 'number'))]" widget="many2many_tags" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                    <field invisible="1" name="dimension_field_ids" placeholder="Choose the fields to aggregate the data" domain="[('table_id', '=', table_id), ('field_type', 'not in', ('numeric', 'number'))]" widget="many2many_tags" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                </xpath>
                <xpath expr="//group[@name='right-side']" position="inside">
                    <field name="analysis_data" widget="izi_analysis" nolabel="1" />
                </xpath>
                <xpath expr="//button[@name='get_analysis_data']" position="after">
                    <button name="get_analysis_data_amchart" type="object" context="{'test_analysis_amchart': True}" string="Test Amchart" class="mb16 btn btn-secondary" icon="fa-bug"/>
                </xpath>
                <xpath expr="//field[@name='drilldown_dimension_ids']//field[@name='name_alias']" position="after">
                    <field name="visual_type_id" options="{'no_create': True, 'no_create_edit':True}"/>
                </xpath>
                <xpath expr="//notebook" position="inside">
                    <page string="Render Visual Script" invisible="1">
                        <p> Script to render visual in JS format.<br/>
                        You can use these variables: visual.title, visual.idElm, visual.data, visual.dimesion and visual.metric.</p>
                        <group>
                            <field string="Enable" name="use_render_visual_script" widget="boolean_toggle"/>
                        </group>
                        <field name="render_visual_script" widget="ace" invisible="use_render_visual_script == False"/>
                    </page>
                </xpath>
                <xpath expr="//notebook/page[3]" position="after">
                    <page string="Number Format">
                        <field name="analysis_number_formats">
                            <tree editable="bottom">
                                <field name="digit" widget="integer"/>
                                <field name="label"/>
                            </tree>
                        </field>
                    </page>
                </xpath>
            </field>
        </record>

        <record model="ir.ui.view" id="izi_dashboard.izi_analysis_form_without_footer">
            <field name="name">izi.analysis.form</field>
            <field name="model">izi.analysis</field>
            <field name="inherit_id" ref="izi_data.izi_analysis_form_without_footer"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='left-side']" position="inside">
                    <field invisible="1" name="visual_type_id" required="1" placeholder="Choose the type of the charts" widget="selection" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                    <field invisible="1" name="metric_field_ids" placeholder="Choose the quantitative fields to measure" domain="[('table_id', '=', table_id), ('field_type', 'in', ('numeric', 'number'))]" widget="many2many_tags" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                    <field invisible="1" name="dimension_field_ids" placeholder="Choose the fields to aggregate the data" domain="[('table_id', '=', table_id), ('field_type', 'not in', ('numeric', 'number'))]" widget="many2many_tags" options="{'no_create': True, 'no_quick_create': True, 'no_create_edit':True}"/>
                </xpath>
                <xpath expr="//group[@name='right-side']" position="inside">
                    <field name="analysis_data" widget="izi_analysis" nolabel="1" />
                </xpath>
                <xpath expr="//button[@name='get_analysis_data']" position="after">
                    <button name="get_analysis_data_amchart" type="object" context="{'test_analysis_amchart': True}" string="Test Amchart" class="mb16 btn btn-secondary" icon="fa-bug"/>
                </xpath>
                <xpath expr="//field[@name='drilldown_dimension_ids']//field[@name='name_alias']" position="after">
                    <field name="visual_type_id" options="{'no_create': True, 'no_create_edit':True}"/>
                </xpath>
                <xpath expr="//notebook" position="inside">
                    <page string="Render Visual Script" invisible="1">
                        <p> Script to render visual in JS format.<br/>
                        You can use these variables: visual.title, visual.idElm, visual.data, visual.dimesion and visual.metric.</p>
                        <group>
                            <field string="Enable" name="use_render_visual_script" widget="boolean_toggle"/>
                        </group>
                        <field name="render_visual_script" widget="ace" invisible="use_render_visual_script == False"/>
                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="izi_dashboard" model="ir.ui.view">
            <field name="name">izi.dashboard</field>
            <field name="model">izi.dashboard</field>
            <field name="type">izidashboard</field>
            <field name="arch" type="xml">
                <izidashboard string="Dashboard">
                </izidashboard>
            </field>
        </record>

        <record id="izi_dashboard_form" model="ir.ui.view">
            <field name="name">izi.dashboard.form</field>
            <field name="model">izi.dashboard</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="theme_id"/>
                                <field name="new_block_position" invisible="1"/>
                                <field name="sequence"/>
                                <field name="rtl" widget="boolean_toggle"/>
                                <field name="animation" widget="boolean_toggle"/>
                            </group>
                            <group>
                                <field name="date_format"/>
                                <field name="start_date" invisible="date_format != 'custom'"/>
                                <field name="end_date" invisible="date_format != 'custom'"/>
                                <field name="write_date" invisible="1"/>
                                <field name="write_uid" invisible="1"/>
                                <field name="refresh_interval"/>
                                <field name="category_id" string="Dashboard Group"/>
                                <field name="use_sidebar"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Filters">
                                <field name="filter_ids">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="source_type"/>
                                        <field name="parent_filter_id"/>
                                        <field name="selection_type"/>
                                    </tree>
                                    <form>
                                        <sheet>
                                            <group>
                                                <group>
                                                    <field name="name"/>
                                                    <field name="source_type"/>
                                                    <field name="selection_type"/>
                                                    <field name="dashboard_id" invisible="1"/>
                                                    <field name="parent_filter_id"/>
                                                </group>
                                                <group>
                                                    <field name="value_ids" widget="many2many_tags" required="source_type == 'predefined'" invisible="source_type != 'predefined'"/>
                                                    <field name="model_id" required="source_type == 'model'" invisible="source_type != 'model'"/>
                                                    <field name="model_field_id" domain="[('model_id', '=', model_id)]" required="source_type == 'model'" invisible="source_type != 'model'"/>
                                                    <field name="model_field_values" required="source_type == 'model'" invisible="source_type != 'model'"/>
                                                    <field name="table_id" required="source_type == 'table'" invisible="source_type != 'table'"/>
                                                    <field name="table_field_id" domain="[('table_id', '=', table_id)]" required="source_type == 'table'" invisible="source_type != 'table'"/>
                                                </group>
                                                <group>
                                                </group>
                                                <group>
                                                    <field name="model_relation_field_id" invisible="1"/>
                                                    <field name="model_field_type" invisible="1"/>
                                                    <field name="model_order_field_id" string="Order By" domain="[('model_id', '=', model_relation_field_id)]" invisible="model_field_type != 'many2one'"/>
                                                    <field name="table_order_field_id" string="Order By" domain="[('table_id', '=', table_id)]" invisible="source_type != 'table'"/>
                                                    <field name="limit" string="Displayed Data" required="1"/>
                                                </group>
                                            </group>
                                            <notebook>
                                                <page string="Applied To">
                                                    <field name="filter_analysis_ids">
                                                    <tree editable="bottom">
                                                        <field name="table_id" create="0"/>
                                                        <field name="allowed_analysis_ids" optional="hide"/>
                                                        <field name="analysis_id" create="0"/> <!-- domain="[('id', 'in', allowed_analysis_ids)]" -->
                                                        <field name="allowed_field_ids" optional="hide"/>
                                                        <field name="field_id" domain="[('id', 'in', allowed_field_ids)]" create="0"/>
                                                        <field name="operator"/>
                                                    </tree>
                                                    </field>
                                                </page>
                                            </notebook>
                                        </sheet>
                                    </form>
                                </field>
                            </page>
                            <page string="Analysis Blocks" invisible="1">
                                <field name="block_ids">
                                    <tree>
                                        <field name="name"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Presentation">
                                <field name="slide_ids">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="slide_title" />
                                        <field name="analysis_id" />
                                        <field name="layout" />
                                    </tree> 
                                </field>
                                <group>
                                    <field name="transition" />
                                    <field name="theme" />
                                    <field name="general_bg_file" filename="general_bg_filename" options="{'mimetype': 'image/*'}"/>
                                    <field name="general_bg_filename" invisible="1"/>
                                    <field name="is_repeat" />
                                    <field name="auto_slide" />
                                </group>
                            </page>
                            <page string="Number Format">
                                <field name="dashboard_number_formats">
                                    <tree editable="bottom">
                                        <field name="digit" widget="integer"/>
                                        <field name="label"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Access Control">
                                <field name="group_ids">
                                    <tree>
                                        <field name="display_name"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Menu Shortcut">
                                <field name="menu_ids">
                                    <tree>
                                        <field name="display_name"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="AI Settings">
                                <group>
                                    <group>
                                        <field name="table_id"/>
                                        <field name="lang_id"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <footer class="izi_replace_footer">
                        <button class="btn btn-primary" string="Save &amp; Close" name="action_save_and_close" type="object"/>
                        <button string="Present Slides" name="action_open_slide" type="object" class="btn-secondary"/>
                        <!-- <button special="cancel" string="Close" class="btn-secondary"/> -->
                        <!-- <button string="Duplicate" name="action_duplicate" type="object"/> -->
                    </footer>
                </form>
            </field>
        </record>

        <record id="izi_dashboard_slide_form" model="ir.ui.view">
            <field name="name">izi.dashboard.slide.form</field>
            <field name="model">izi.dashboard.slide</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="dashboard_id" invisible="1"/>
                                <field name="analysis_domain" invisible="1"/>
                                <field name="analysis_id" />
                                <field name="text_align" />
                                <field name="automatic_font_size" />
                                <field name="font_size" invisible="automatic_font_size == True"/>
                                <field name="automatic_font_color" />
                                <field name="font_color" invisible="automatic_font_color == True" placeholder="white, black, or #f4f4f4"/>
                                <field name="show_logo" />
                            </group>
                            <group>
                                <field name="slide_title" />
                                <field name="layout" />
                                <field name="layout_order" invisible="layout not in ('row','column')"/>
                                <field name="chart_size" invisible="layout not in ('row','column')"/>
                                <field name="text_size" invisible="layout not in ('row','column')" readonly="1"/>
                                <field name="bg_file" filename="bg_filename" options="{'mimetype': 'image/*'}"/>
                                <field name="bg_filename" invisible="1"/>
                            </group>
                        </group>
                        <group>
                            <field name="text_content" />
                        </group>
                        <button string="Generate With AI" class="btn-secondary" invisible="analysis_id == False" type="object" name="action_generate_content_ai"/>
                    </sheet>
                    <footer>
                        <button string="Save" name="action_save_only" type="object" class="btn-primary"/>
                        <!-- <button string="Discard" class="btn-secondary" special="cancel"/> -->
                    </footer>
                </form>
            </field>
        </record>
        
    </data>
</odoo>
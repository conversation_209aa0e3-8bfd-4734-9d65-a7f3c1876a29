<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="0">
    <record model="ir.module.category" id="module_category_izi_dashboard">
      <field name="name">IZI Dashboard</field>
      <field name="description">Helps you handle your dashboard.</field>
      <field name="sequence">200</field>
    </record>

    <record id="group_user_dashboard" model="res.groups">
      <field name="name">User: Own Dashboard Only</field>
      <field name="category_id" ref="izi_dashboard.module_category_izi_dashboard"/>
      <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
      <field name="comment">the user will have access to his own dashboard in the IZI Dashboard application.</field>
    </record>

    <record id="group_manager_dashboard" model="res.groups">
      <field name="name">Manager: All Dashboard</field>
      <field name="category_id" ref="izi_dashboard.module_category_izi_dashboard"/>
      <field name="implied_ids" eval="[(4, ref('group_user_dashboard'))]"/>
      <field name="comment">the user will have access to all dashboard of everyone in the IZI Dashboard application.</field>
      <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
      <field name="groups_id" eval="[(4,ref('izi_dashboard.group_manager_dashboard'))]"/>
    </record>

    <record id="izi_dashboard_rule" model="ir.rule">
      <field name="name">Dashboard</field>
      <field name="model_id" ref="model_izi_dashboard"/>
      <field name="domain_force">['|', ('group_ids', '=', False), ('group_ids', 'in', user.groups_id.ids)]</field>
    </record>
  </data>
</odoo>
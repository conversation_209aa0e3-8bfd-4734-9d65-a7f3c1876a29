.izi_view {
    width: 100%;
    height: 100%;
    display: block;
    background: white;
    /* font-family: 'Roboto'; */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Ubuntu, "Noto Sans", <PERSON><PERSON>, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    color: #374151;
    overflow-y: auto;
}
.izi_flex{
    display: flex;
}

.izi_view_analysis {
    box-sizing: border-box;
    width: 80%;
    height: 100%;
    display: flex;
    flex-flow: row;
    padding: 15px;
    background: #E9ECEF;
    z-index: 9;
}

.modal-dialog .izi_view_analysis,
.modal-dialog .izi_config_analysis {
    height: 600px;
}

.izi_view_analysis .izi_dashboard_block_item{
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}

.izi_config_analysis {
    background: white;
    box-sizing: border-box;
    width: 20%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: 1px solid #EEE;
    border-top: 1px solid #EEE;
    z-index: 8;
}

.izi_add_dashboard_block {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.izi_view_dashboard {
    background: #E9ECEF;
    box-sizing: border-box;
    width: 100%;
    height: auto;
    min-height: 150%;
    display: flex;
    flex-flow: column wrap;
    padding: 10px;
    position: relative;
}

.izi_view_visual {
    position: relative;
    display: flex;
    padding: 0px;
    /* margin: 20px; */
    margin-top: 0px;
}
.izi_view_visual .izi_reset_drilldown {
    display: none;
    z-index: 10;
    background: #F4F4F4;
    border-radius: 8px;
    font-size: 14px;
    width: 40px;
    position: absolute;
    top: 0px;
    left: 50%;
    margin-left: -20px;
    opacity: 0.5;
}
.izi_view_visual .izi_reset_drilldown:hover {
    opacity: 1;
}
.izi_view_visual .izi_drillup {
    display: none;
    z-index: 10;
    background: #F4F4F4;
    border-radius: 8px;
    font-size: 14px;
    width: 40px;
    position: absolute;
    top: 0px;
    left: 45%;
    margin-left: -20px;
    opacity: 0.5;
}
.izi_view_visual .izi_drillup:hover {
    opacity: 1;
}
.izi_view_visual .gridjs-container {
    font-size: 13px;
    margin: 20px;
    margin-top: 10px;
    overflow: auto;
}

.izi_view_visual .izi_table_container{
    width: 100%;
    padding: 0px 20px;
    overflow: scroll;
    /* margin-top: -6px; */
}

.izi_view_visual .izi_table_container .gridjs-container{
    margin: 0px;
}

.izi_view_background{
    background: #E9ECEF;
    display: flex !important;
    justify-content: center;
    align-items: center !important;
    flex-wrap: wrap;
}

.izi_dashboard_block_item_v_background{
    background: #E9ECEF !important;
    box-shadow: none !important;
}

/* Explore Analysis */
.izi_view_analysis_explore_header {
    position: fixed;
    top: 20px;
    right: 50px;
    left: 50px;
    height: 80px;
    background: #E9ECEF;
    z-index: 1000;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.izi_view_analysis_explore {
    position: fixed;
    top: 100px;
    right: 50px;
    left: 50px;
    bottom: 80px;
    padding: 10px;
    background: white;
    z-index: 1000;
    overflow: scroll;
    font-size: 8px;
}

.izi_view_analysis_explore_footer {
    position: fixed;
    bottom: 20px;
    right: 50px;
    left: 50px;
    height: 60px;
    background: #E9ECEF;
    z-index: 1000;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.izi_view_analysis_explore .izi_view_analysis_explore_container {    
    float: left;
    width: 24%;
    height: 300px;
    background: transparent;
    background-color: white;
    margin: 0.5%;
    border-radius: 20px;
    box-sizing: border-box;
    padding: 10px;
    border: 1px solid white;
    box-shadow: 0px 0px 15px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.izi_view_analysis_explore .izi_view_analysis_explore_container:hover,
.izi_view_analysis_explore .izi_view_analysis_explore_container.active {    
    border: 1px solid #06c;
}


.izi_view_analysis_explore .izi_view_analysis_explore_container.active {
    border: 4px solid #06c;
}

.izi_view_analysis_explore_bg {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: black;
    opacity: 0.55;
    z-index: 9;
}

.izi_view_analysis_explore_footer > .izi_btn {
    float: right;
    margin-left: 10px;
}

.izi_view_analysis_explore_header {
    padding: 15px;
    font-size: 20px;
    background: white;
    font-weight: bold;
    text-align: center;
}

.izi_view_analysis_explore_header .subtitle{
    font-weight: 300;
    font-size: 14px;
    display: block;
}

.izi_view_analysis_explore_footer {
    background: white;
    padding: 10px 20px;
}

.izi_view_analysis_explore_content {
    width: 100%;
    height: 100%;
}
.izi_view_analysis_explore_title {
    font-size: 10px;
    margin-bottom: -5px;
    font-weight: 300;
    text-align: center;
}

.izi_view_analysis_explore_footer .izi_select2 {
    float: right;
    width: 250px;
}

.izi_view_analysis_explore_footer .izi_select2 .select2-choice{
    height: 34px;
    margin-top: 0px;
    margin-right: 6px;
}

.izi_view_analysis_explore_footer .izi_select2 .select2-chosen {
    line-height: 32px;
}

/* Explore Dashboard */
.izi_view_dashboard_ask_discuss {
    margin: 8px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
    width: 50%;
    border-radius: 8px;
    overflow: hidden;
}
.izi_dialog .izi_view_dashboard_ask_header {
    position: fixed;
    top: 20px;
    right: 80px;
    left: 80px;
}
.izi_dialog .izi_view_dashboard_ask {
    position: fixed;
    top: 100px;
    right: 80px;
    left: 80px;
    bottom: 120px;
}
.izi_dialog  .izi_view_dashboard_ask_footer {
    position: fixed;
    bottom: 20px;
    right: 80px;
    left: 80px;
}
.izi_view_dashboard_ask_header {
    height: auto;
    background: #E9ECEF;
    /* z-index: 1000; */
}

.izi_view_dashboard_ask {
    height: 380px;
    background: white;
    /* z-index: 1000; */
    overflow: scroll;
    font-size: 13px;
    padding: 20px;
    padding-top: 20px;
    padding-bottom: 40px;
}
.izi_view_dashboard_ask .role_section:first-of-type {
    margin-top: 0px;
}
.izi_view_dashboard_ask .role_section {
    position: relative;
    margin-top: 16px;
    font-weight: bold;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
}
.izi_view_dashboard_ask .role_section > .role_avatar {
    background: #888;
    color: white;
    height: 32px;
    width: 32px;
    float: left;
    margin-right: 10px;
    border-radius: 100px;
    font-size: 14px;
    font-weight: bold;
    line-height: 32px;
    text-align: center;
}
.izi_view_dashboard_ask .message_section .code_content {
    font-family: JetBrainsMono;
    font-size: 12px;
    background: #F4F4F4;
    padding: 12px;
    border-radius: 6px;
    height: 0px;
    overflow: hidden;
    color: transparent;
}
.izi_view_dashboard_ask .message_section .code_content:hover {
    height: auto;
    color: inherit;
} 
.izi_view_dashboard_ask .message_section .code_content .code_execution {
    display: block;
    float: right;
    margin-top: -30px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    cursor: pointer;
    text-align: center;
    cursor: pointer;
    border-radius: 6px;
    padding: 8px;
    box-sizing: content-box;
}
.izi_view_dashboard_ask .message_section .code_content .code_execution .material-icons{
    font-size: 16px;
}
.izi_view_dashboard_ask .message_section .code_content .code_execution:hover {
    background: #DDD;
}
.izi_view_dashboard_ask .message_section {
    padding-left: 42px;
}

.izi_view_dashboard_ask_footer {
    position: relative;
    height: 100px;
    background: #E9ECEF;
    background: white;
    padding: 10px 20px;
    /* z-index: 1000; */
}

.izi_view_dashboard_ask_bg {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: black;
    opacity: 0.55;
    z-index: 9;
}

.izi_view_dashboard_ask_footer > .izi_btn {
    float: right;
    margin-left: 10px;
}

.izi_view_dashboard_ask_header {
    padding: 20px;
    padding-bottom: 16px;
    font-size: 16px;
    background: white;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid #EEE;
}

.izi_view_dashboard_ask_header .material-icons{
    display: block;
    float: left;
    margin-right: 2%;
    width: 4%;
    height: 50px;
}
.izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_title{
    display: block;
    float: left;
    width: 94%;
}
.izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_table{
    display: flex;
    align-items: center;
    float: left;
    padding: 2px 12px;
    font-size: 12px;
    border-radius: 100px;
    background-color: #F4F4F4;
    margin-top: 4px;
    font-weight: 300;
    cursor: pointer;
}
.izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_table .izi_dashboard_table_selection a{
    border: 0;
    background: none;
    padding-left: 1em;
    height: 20px;
    line-height: 1.6;
    margin-top: 0px;
    padding-left: 8px;
    padding-right: 20px;
}
.izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_table .izi_dashboard_table_selection a .select2-chosen{
    font-weight: bold;
    font-size: 12px;
    font-weight: 600;
    margin-right: 0px;
}

.izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_table .izi_dashboard_table_selection .izi_select2.select2-container .select2-choice .select2-arrow {
    margin-top: -3px;
}

.izi_view_dashboard_ask_header .subtitle{
    font-weight: 300;
    font-size: 14px;
    display: block;
}

.izi_view_dashboard_ask_content {
    width: 100%;
    height: 100%;
}
.izi_view_dashboard_ask_title {
    font-size: 10px;
    margin-bottom: -5px;
    font-weight: 300;
    text-align: center;
}

.izi_view_dashboard_ask_footer .izi_select2 {
    float: right;
    width: 250px;
}

.izi_view_dashboard_ask_footer .izi_select2 .select2-choice{
    height: 34px;
    margin-top: 0px;
    margin-right: 6px;
}

.izi_view_dashboard_ask_footer .izi_select2 .select2-chosen {
    line-height: 32px;
}

.izi_view_dashboard_ask_input {
    border-radius: 8px;
    border: 1px solid #DDD;
    height: 70px;
    resize: none !important;
    padding: 10px;
    padding-right: 60px;
}

.izi_view_dashboard_ask_btn {
    position: absolute;
    top: 25px;
    right: 35px;
    padding: 10px;
    background: #F4F4F4;
    border-radius: 10px;
    cursor: pointer;
    height: 38px;
    width: 38px;
}

/* Ask Result */
.izi_view_dashboard_ask_container {
    width: 100%;
    display: flex;
}
.izi_view_dashboard_ask_result {
    margin: 8px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
    background: white;
    width: 50%;
    border-radius: 8px;
    overflow-x: hidden;
    overflow-y: auto;
}
.izi_view_dashboard_ask_result_title {
    height: 10%;
    position: relative;
    text-align: right;
    padding-top: 30px;
    padding-right: 30px;
}
.izi_view_dashboard_ask_result_chart.izi_dashboard_block_content {
    height: 90%;
    padding: 20px;
    padding-top: 0px;
}
.izi_view_dashboard_ask_result_explanation {
}
.izi_view_dashboard_ask_result_configuration,
.izi_view_dashboard_ask_result_add_to_dashboard {
    float: right;
    margin-left: 8px;
    display: flex;
    padding: 2px 12px;
    font-size: 12px;
    border-radius: 100px;
    font-weight: 300;
    cursor: pointer;
    background-color: #FFFFFF;
    border: 2px solid #06c;
    color: #06c;
}
.izi_view_dashboard_ask_result_configuration .material-icons.izi_btn_icon,
.izi_view_dashboard_ask_result_add_to_dashboard .material-icons.izi_btn_icon{
    font-size: 12px;
    margin-right: 4px;
    line-height: 18px;
}
.izi_view_dashboard_ask_result_configuration:hover,
.izi_view_dashboard_ask_result_add_to_dashboard:hover {
    background: #06c;
    color: white;
}
.izi_view_dashboard_ask_container .message_refresh {
    position: absolute;
    display: block;
    right: 20px;
    top: 0px;
    cursor: pointer;
    height: 20px;
    width: 20px;
    color: #EEE;
}
.izi_view_dashboard_ask_container .message_refresh:hover {
    color: #000;
}
.izi_view_dashboard_ask_container .message_refresh .material-icons{
    font-size: 20px;
}
.izi_view_dashboard_ask_container .izi_view_dashboard_ask_quick_messages {
    position: absolute;
    bottom: 90px;
    overflow: auto;
    background: white;
    height: 30px;
    width: 100%;
    padding-top: 8px;
    box-sizing: content-box;
}
.izi_view_dashboard_ask_container .izi_view_dashboard_ask_quick_messages:hover {
    height: auto;
    min-height: 30px;
}
.izi_view_dashboard_ask_container .quick_message {
    float: left;
    font-size: 12px;
    padding: 2px 12px;
    background: #F4F4F4;
    border-radius: 100px;
    margin-right: 8px;
    margin-bottom: 8px;
    font-weight: 200;
    cursor: pointer;
}
.izi_view_dashboard_ask_container .quick_message:hover {
    background-color: #06c;
    color: white;
}
.izi_view_dashboard_ask_container .clear_message {
    float: left;
    font-size: 12px;
    padding: 2px 12px;
    background: #F4F4F4;
    border-radius: 100px;
    margin-right: 8px;
    margin-bottom: 8px;
    font-weight: 200;
    cursor: pointer;
}
.izi_view_dashboard_ask_container .clear_message:hover {
    background-color: #ff0055;
    color: white;
}


/* View Analysis Dialog */
.izi_view_analysis_dialog_bg {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: black;
    opacity: 0.55;
    z-index: 9;
}
.izi_view_analysis_dialog_main {
    position: fixed;
    top: 40px;
    right: 40px;
    left: 40px;
    bottom: 40px;
    padding: 0px;
    background: white;
    z-index: 1000;
    overflow: scroll;
    font-size: 14px;
    border-radius: 8px;
}
.izi_view_analysis_dialog_main
.izi_dashboard_block_header 
.izi_dashboard_block_title{
    margin: 0px;
    /* font-family: 'Roboto'; */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 15px;
}
.izi_view_analysis_dialog_main
.izi_dashboard_block_header 
.izi_dashboard_block_title.dropdown-toggle::after {
    vertical-align: 0.2em;
    margin-left: 0.6em;
    font-size: 14px;
}
.izi_view_analysis_dialog_main
.izi_dashboard_block_content {
    padding: 10px;
}
.izi_view_analysis_dialog_main
.izi_analysis_filter_temp_container {
    display: none;
}

/* Script Editor */
.izi_view_analysis .izi_dashboard_block_item {
    width: auto;
    flex-grow: 1;
}
.izi_view_analysis .izi_dashboard_script_editor {
    display: none;
    position: relative;
    right: 0px;
    top: 0px;
    left: 0px;
    bottom: 0px;
    margin: 0px;
    width: 500px;
    flex-basis: 500px;
    flex-shrink: 0;
    margin-left: 0px;
    height: 100%;
    padding: 20px;
    padding-bottom: 60px;
    border-radius: 8px;
    box-shadow: 0px 0px 20px 2px rgba(0, 0, 0, 0.1);
}
.izi_view_analysis .izi_dashboard_script_editor.dark-mode {
    background: #272823;
}
.izi_view_analysis .izi_dashboard_script_editor.dark-mode h4{
    color: white;
}
.izi_view_analysis .izi_dashboard_script_editor.light-mode {
    background: white;
}
.izi_view_analysis .izi_dashboard_script_editor.light-mode h4{
    color: black;
}
.izi_view_analysis .izi_dashboard_script_editor.floating {
    position: fixed;
    background: white;
    padding: 20px;
    padding-bottom: 60px;
    border-radius: 8px;
    box-shadow: 0px 0px 20px 2px rgba(0, 0, 0, 0.1);
    z-index: 10000;
}
.izi_dashboard_script_editor .izi_dashboard_script_type {
    position: absolute;
    z-index: 20;
    right: 25px;
    bottom: 25px;
    height: 60px;
    border-radius: 8px;
    padding: 5px;
}
.izi_dashboard_script_editor .izi_dashboard_script_close {
    position: absolute;
    z-index: 20;
    right: 10px;
    top: 10px;
    font-size: 24px;
    padding: 5px;
    color: #666;
    cursor: pointer;
}
.izi_dashboard_script_editor .izi_dashboard_script_close:hover {
    color: white;
}
.izi_dashboard_script_editor h4 {
    font-size: 18px;
    font-weight: bold;
}
.izi_dashboard_script_editor h4 > span{
    font-weight: 200;
}
.izi_dashboard_script_editor .izi_dashboard_script_editor_content {
    display: flex;
    flex-direction: column;

    height: 95%;
    width: 100%;
    border: 1px solid #dee2e6;
}
.izi_dashboard_script_editor .ace-chrome .ace_gutter {
    background-color: #F4F4F4;
    color: #666;
}
.izi_dashboard_script_editor .ace_scroller {
    /* border: 1px solid #dee2e6; */
    overflow: scroll;
}
.izi_dashboard_script_editor .izi_script_tab{
    width: 100%;
    height: 32px;
    padding-left: 48px;
    /* background-color: #ebebeb; */
    background-color: #F4F4F4;
    color: #666;
    display: flex
}
.izi_dashboard_script_editor .izi_script_tab .izi_script_tab_button.izi_tab_active {
    background-color: #ffffff;
    border-top: 2px solid #06c;
    /* opacity: 1; */
    /* border-bottom: 0;*/
}
.izi_dashboard_script_editor .izi_script_tab .izi_script_tab_button {
    display: flex;
    justify-content: center;
    align-items: center;
    /* height: 30px; */
    width: 80px;
    /* background-color: #8d8d8d; */
    border-bottom: 0;
    /* margin-left: 2px; */
    /* cursor: pointer; */
    /* border: 3px solid #ebebeb; */
    /* opacity: 0.6;*/
}
.izi_dashboard_script_editor #izi_script_editor, .izi_dashboard_script_editor #izi_script_editor_css{
    /* height: 100%; */
    height: 96%;
    width: 100%;

    flex-grow: 1;
}
.izi_dashboard_script_editor .izi_dashboard_script_editor_button_container {
    display: block;
    padding: 10px 0px;
}
.izi_dashboard_script_editor .izi_dashboard_script_editor_button_container > .izi_btn {
    float: left;
    display: block;
    font-size: 13px;
    border-radius: 4px;
}
.izi_dashboard_script_editor.dark-mode .izi_dashboard_script_editor_button_container > .izi_float_button {
    float: left;
    cursor: pointer;
    color: #666;
}
.izi_dashboard_script_editor.light-mode .izi_dashboard_script_editor_button_container > .izi_float_button {
    float: left;
    cursor: pointer;
    color: #AAA;
}
.izi_dashboard_script_editor.dark-mode .izi_dashboard_script_editor_button_container > .izi_float_button:hover {
    color: white;
}
.izi_dashboard_script_editor.light-mode .izi_dashboard_script_editor_button_container > .izi_float_button:hover {
    color: black;
}
.izi_dashboard_script_editor .izi_dashboard_script_editor_button_container > .izi_float_button > .material-icons {
    padding: 4px 6px;
}
#ask_ai_container {
    width: 600px;
    max-width: 100vw;
    max-height: 100vh;
    position: absolute;
    top: 20vh;
    right: 30px;
    opacity: 0;
    transform: translateY(20px); /* Start slightly below */
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    /* display: none; */
}
#ask_ai_container.active {
    opacity: 1;
    transform: translateY(0); /* Move to original position */
    /* display: block; */
}
@media only screen and (max-width: 767px) {
    #ask_ai_container {
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
    #ask_ai_container .izi_view_dashboard_ask {
        height : 60vh !important;
    }
    .izi_view_dashboard_ask_header .izi_view_dashboard_ask_header_title {
        width: 89% !important;
        margin-left: 3% !important;
    }
}
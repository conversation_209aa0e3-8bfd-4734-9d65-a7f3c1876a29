.izi_config_dashboard {
    background: white;
    box-sizing: border-box;
    border-top: 1px solid #EEE;
}
.izi_select_dashboard {
    cursor: pointer;
    border-right: 1px solid #EEE;
    /* display: flex;
    align-items: center; */
}
/* .izi_select_dashboard > div {
    display: flex;
    justify-content: space-between;
    flex: 1;
} */
.izi_config_dashboard_button_container .izi_dropdown {
    height: 36px;
    line-height: 36px;
}
.izi_config_dashboard_button_container .izi_dropdown .dropdown-menu .dropdown-item {
    padding: 4px 20px !important;
}
.izi_view .izi_config_dashboard_button_container .dropdown-toggle::after {
    display: none;
}
.izi_config_dashboard_button_container .izi_btn {
    margin: 0px;
    margin-left: 4px;
    font-size: 13px;
    padding: 0px 8px;
    font-weight: 300;
    font-family: 'Roboto-Light';
    border: none;
    height: 36px;
    line-height: 36px;
}
.izi_config_dashboard_button_container .izi_btn > span{
    float: left;
    font-size: 18px;
    height: 36px;
    line-height: 36px;
    margin-right: 4px;
}
.izi_select_dashboard .izi_edit_dashboard_button,
.izi_select_dashboard .izi_save_dashboard_button {
    font-size: 22px;
    height: 30px;
    line-height: 30px;
    margin-left: 8px;
}
.izi_select_dashboard .izi_title .izi_input {
    /* color: black; */
    color: #374151;
    font-weight: 400;
    font-size: 18px;
    background: transparent;
}
.izi_dashboard_filter {
    /* min-width: 150px; */
    height: 36px;
    line-height: 36px;
    color: #444;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    border: 1px solid #E4E4E4;
    border-radius: 10px;
    box-sizing: content-box;
    margin-left: 6px;
    display: flex;
}
.izi_dashboard_filter.disable_filter,
.izi_dashboard_filter.disable_filter .izi_select2
{
    opacity: 50%;
    cursor: not-allowed;
}
.izi_dashboard_filter.disable_filter .select2-choice{
    pointer-events: none;
}
.izi_dashboard_filter .select2-container {
    vertical-align: unset;
    display: flex;
    height: 36px;
    flex-direction: column;
    justify-content: center;
    padding-right: 12px;
}
.izi_dashboard_filter > .select2-container {
    /* padding-left: 12px; */
    padding-left: 3px;
}
.izi_dashboard_filter .izi_select2 .select2-choices,
.izi_dashboard_filter .izi_select2 .select2-choice{
    border: none !important;
    color: #444;
}
.izi_dashboard_filter input.izi_datepicker {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
    margin: 0px;
    color: #444;
    border-radius: 10px;
}
.izi_dashboard_filter input.izi_datepicker::placeholder {
    color: #444;
    opacity: 1; /* Firefox */
}
.izi_dashboard_filter input.izi_datepicker::-ms-input-placeholder { /* Edge 12 -18 */
    color: #444;
}
.izi_dashboard_filter .izi_select2 .select2-choices {
    display: flex;
    font-size: 13px;
    height: 36px;
    line-height: 36px;
    margin: 0px;
}
.izi_dashboard_filter .izi_select2 .select2-choice {
    font-size: 13px;
    height: 36px;
    line-height: 36px;
    margin: 0px;
}
.izi_dashboard_filter .izi_select2 .select2-default {
    font-size: 13px;
    color: #444 !important;
    margin-left: 0px !important;
    padding-left: 0px !important;
    margin: 0;
    height: 36px;
    line-height: 36px;
}
.izi_dashboard_filter .izi_select2 .select2-chosen {
    font-size: 13px;
    color: #444 !important;
    height: 36px;
    line-height: 36px;
}
.izi_dashboard_filter .select2-container .select2-choice .select2-arrow {
    display: none;
}
.izi_dashboard_filter .izi_select2.select2-container-multi .select2-choices .select2-search-field input {
    max-width: 100px;
}
.izi_dashboard_filter .izi_select2 .select2-chosen {
    font-size: 13px;
}
.izi_dashboard_filter .izi_select2 .select2-search-choice {
    max-width: 100px;
    white-space: nowrap;
    text-overflow: ellipsis;
    /* overflow: hidden; */
}
.izi_dashboard_filter .izi_select2 .select2-search-choice > div{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.izi_dashboard_filter_title {
    float: left;
    width: auto;
}
.izi_dashboard_filter_title span{
    height: 28px;
    line-height: 28px;
    margin: 4px;
    padding-left: 10px;
    padding-right: 4px;
    background: transparent;
    font-size: 16px;
    color: #666;
    border-radius: 100px;
    border-bottom-left-radius: 100px;
    border-top-left-radius: 100px;
}
.izi_dashboard_filter_content {
    float: left;
    min-width: 142px;

    display: flex;
    flex-direction: row;
}
.izi_dashboard_filter_content .izi_dropdown {
    width: 100%;
}
.izi_dashboard_filter_content .dropdown-toggle {
    height: 36px;
    line-height: 36px;
    width: 100%;
    display: block;
    text-align: left;
    padding-left: 0px;
    background: none;
    font-weight: 300;
    font-size: 13px;
    color: #444;
}
.izi_dashboard_filter_content .dropdown-toggle:focus {
    outline: none;
}
.izi_dashboard_filter_content .dropdown-toggle::after {
    display: none;
}
.izi_dashboard_filter_content a.dropdown-item.izi_select_date_format {
    height: 26px;
    line-height: 26px;
    box-sizing: content-box;
    width: auto;
}
.day.range-day {
    background-color: #e1d2ff;
}
.izi_dashboard_filter#izi_dashboard_filter_date_range {
    min-width: 325px;
}
.izi_dashboard_filter#izi_dashboard_filter_date_range .izi_dashboard_filter_content {
    width: 100%;
}
.izi_dashboard_filter#izi_dashboard_filter_date_range .izi_dashboard_filter_content .o_datepicker{
    float: left;
    width: 50%;
    padding-left: 10px;
}
.izi_dashboard_filter#izi_dashboard_filter_date_range .izi_dashboard_filter_content .o_datepicker input{
    border: 0px;
    height: 32px;
    line-height: 32px;
    background: none;
    font-size: 13px;
}
.izi_dashboard_filter#izi_dashboard_filter_date_range .izi_dashboard_filter_content .o_datepicker input::placeholder{
    font-weight: 300;
    font-size: 13px;
    color: #444;
}
.izi_dashboard_filter .izi_select2.select2-container-multi .select2-choices .select2-search-choice {
    border: 1px solid #DDD;
    color: black;
    /* padding: 3px 0px 3px 18px; */
}
#izi_dashboard_ai_search_input {
    width: 400px;
    padding: 0px 10px;
    border: none;
    height: 32px;
    box-sizing: border-box;
    background: none;
    font-weight: 300;
    font-size: 13px;
    color: #444;
    line-height: 32px;
}
#izi_dynamic_filter_container {
    display: flex;
}
.izi_dashboard_filter#izi_dashboard_search_container {
    min-width: 650px;
}
.izi_dashboard_filter#izi_dashboard_search_container .izi_dashboard_filter_content {
    min-width: 600px;
}
.izi_dashboard_filter#izi_dashboard_search_container .izi_select2.select2-container-multi .select2-choices .select2-search-field input {
    max-width: 600px;
}
.izi_dashboard_filter#izi_dashboard_search_container .izi_select2 .select2-search-choice {
    width: auto;
}
.izi_dashboard_option {
    display: flex;
    margin: -6px -16px;
    padding: 6px 16px;
    padding-bottom: 8px;
    padding-top: 8px;
    border-bottom: 1px solid #EEE;
}
.izi_dashboard_option .izi_dashboard_option_name {    
    color: #444;
    display: flex;
    height: 24px;
    line-height: 24px;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: -0.5px;
}
.izi_dashboard_option .izi_dashboard_option_header{
    flex: 1;
    display: flex;
    flex-direction: column;
}
.izi_dashboard_option .izi_dashboard_option_visual{
    width: 0px;
    text-align: right;
}
.izi_dashboard_option .izi_dashboard_option_category {
    margin: 0px;
    /* display: none; */
}
.izi_dashboard_option .izi_dashboard_option_category span {
    float: left;
    font-size: 8px;
    background: #EEE;
    padding: 2px 10px;
    margin: 0px;
    border-radius: 4px;
    color: #888;
    font-weight: 600;
    text-transform: uppercase;
    margin-right: 6px;
    letter-spacing: 0.5px;
    margin-top: 4px;
}
.izi_dashboard_option .izi_dashboard_option_name span.material-icons,
.izi_dashboard_option .izi_dashboard_option_name span.material-icons-outlined,
.izi_dashboard_option .izi_dashboard_option_category span.material-icons,
.izi_dashboard_option .izi_dashboard_option_category span.material-icons-outlined
 {
    font-size: 14px;
    color: #444;
    background: none;
    padding: 0px;
    margin: 0px;
    margin-right: 8px;
    height: 24px;
    line-height: 24px;
    text-transform: none;
}
.izi_dashboard_option .izi_dashboard_option_visual span.material-icons,
.izi_dashboard_option .izi_dashboard_option_visual span.material-icons-outlined {
    font-size: 13px;
    color: #AAA;
}
.izi_dashboard_option .izi_dashboard_option_category span.izi_dashboard_option_premium {
    background: #06c;
    color: white;
}

#izi_sidebar{
    padding: 0;
    margin: 0;
}
.izi_filter_button{
    display: flex;
    justify-content: center;
    align-items: center;
}
.filter_sidebar_background{
    position: fixed;
    z-index: 9999;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: #00000033;
    display: flex;
    justify-content: flex-end;
}
.filter_sidebar_background.filter_hide .filter_sidebar_container{
    transform: translateX(100%)
}
.filter_sidebar_container.desktop_sidebar{
    width: 460px;
}
.filter_sidebar_container{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100vh;
    
    width: 85%;
    background-color: white;
    transform: translateX(0);
    transition: transform 0.3s ease;
}
.filter_sidebar_container.desktop_sidebar #izi_dynamic_filter_container{
    width: 100% !important;
}
.filter_sidebar_container.desktop_sidebar .izi_dashboard_filter{
    width: 100% !important;
    margin: 10px 0px;
}
.filter_sidebar_container.desktop_sidebar .izi_dashboard_filter_content{
    width: 100% !important;
}
.filter_title{
    display: flex;
    justify-content: space-between;
    padding-inline: 20px;
    padding-block: 10px;
    align-items: center;
    font-weight: bold;
    border-bottom: 0.5px solid #eee;
    margin-bottom: .3em;

    min-height: 46px;
    background-color: #f8f9fa;
    box-shadow: inset 0 -1px 0 var(--border-color, #dee2e6);
}
.filter_sidebar_header{
    display: flex;
    justify-content: center;

    margin: 10px 0px;
}
.filter_sidebar_main{
    display: flex;
    justify-content: center;

    flex: 1;
    overflow-y: auto;
    margin-bottom: 10px;
}

/* -- overflow */
#izi_inner_config_dashboard .izi_dashboard_filter_container{
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
}
  


.filter_sidebar_main .izi_dashboard_filter_container{
    display: flex;
    align-items: center;
    flex-direction: column !important;

    width: 90%;

    /* -- overflow */
    /* flex: none;
    overflow-x: unset;
    overflow-y: unset; */
}

.filter_sidebar_footer{
    display: flex;
    justify-content: center;
    position: sticky;
    bottom: 0;
    width: 100%;
    background: white;
    border-top: 1px solid #dbdcdf;
    padding: 15px 0 15px 0;
    /* padding-bottom: env(safe-area-inset-bottom, 16px); */
}

.filter_info_counter{
    position: absolute;
    width: 16px;
    height: 16px;
    background-color: red;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: white;
    font-weight: bold;
    border-radius: 10px;
    top: 26%;
    right: 20%;
    font-size: .7em;
}
.filter_sidebar_main #izi_dynamic_filter_container{
    flex-direction: column;
}

.izi_select2_label{
    white-space: nowrap;
    font-weight: 500;
    text-transform: capitalize;
}
.izi_dashboard_filter_content > .izi_select2_label{
    margin-right: 5px;
}
.izi_dashboard_filter > .izi_select2_label{
    margin-left: 10px;
}

/* .izi_config_dashboard_button_container{
    overflow-y: auto;
} */

@media only screen and (max-width: 792px) {
    .izi_config_dashboard_button_container {
        display: none !important;
    }
    .izi_dialog_content {
        width: 80% !important;
        left: 10% !important;
    }
    .izi_dialog_content .izi_select_item {
        width: 100% !important;
        margin: 5px 0% !important;
    }
    .izi_view.izi_flex {
        display: block !important;
    }
    .izi_config_analysis {
        width: 100% !important;
        padding: 0px !important;
    }
    .izi_view_analysis {
        width: 100% !important;
        padding: 0px !important;
        padding-top: 10px !important;
    }
    .izi_select_dashboard {
        width: 84% !important;
    }

    /* ----- */
    .izi_view hr {
        margin: 0px 0px 10px 0px;
    }

    #izi_dynamic_filter_container{
        width: 100% !important;
    }
    .izi_dashboard_filter{
        width: 100% !important;
        margin: 10px 0px;
    }
    .izi_dashboard_filter_content{
        width: 100% !important;
    }
    
}

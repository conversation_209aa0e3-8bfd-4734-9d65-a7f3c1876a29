/*!
  * Tempus Dominus v6.9.4 (https://getdatepicker.com/)
  * Copyright 2013-2023 <PERSON>
  * Licensed under MIT (https://github.com/Eonasdan/tempus-dominus/blob/master/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).tempusDominus={})}(this,(function(t){"use strict";class e extends Error{}const s="tempus-dominus";class i{}i.NAME=s,i.dataKey="td",i.events=new class{constructor(){this.key=".td",this.change=`change${this.key}`,this.update=`update${this.key}`,this.error=`error${this.key}`,this.show=`show${this.key}`,this.hide=`hide${this.key}`,this.blur=`blur${this.key}`,this.focus=`focus${this.key}`,this.keyup=`keyup${this.key}`,this.keydown=`keydown${this.key}`}},i.css=new class{constructor(){this.widget=`${s}-widget`,this.calendarHeader="calendar-header",this.switch="picker-switch",this.toolbar="toolbar",this.noHighlight="no-highlight",this.sideBySide="timepicker-sbs",this.previous="previous",this.next="next",this.disabled="disabled",this.old="old",this.new="new",this.active="active",this.dateContainer="date-container",this.decadesContainer=`${this.dateContainer}-decades`,this.decade="decade",this.yearsContainer=`${this.dateContainer}-years`,this.year="year",this.monthsContainer=`${this.dateContainer}-months`,this.month="month",this.daysContainer=`${this.dateContainer}-days`,this.day="day",this.calendarWeeks="cw",this.dayOfTheWeek="dow",this.today="today",this.weekend="weekend",this.rangeIn="range-in",this.rangeStart="range-start",this.rangeEnd="range-end",this.timeContainer="time-container",this.separator="separator",this.clockContainer=`${this.timeContainer}-clock`,this.hourContainer=`${this.timeContainer}-hour`,this.minuteContainer=`${this.timeContainer}-minute`,this.secondContainer=`${this.timeContainer}-second`,this.hour="hour",this.minute="minute",this.second="second",this.toggleMeridiem="toggleMeridiem",this.show="show",this.collapsing="td-collapsing",this.collapse="td-collapse",this.inline="inline",this.lightTheme="light",this.darkTheme="dark",this.isDarkPreferredQuery="(prefers-color-scheme: dark)"}},i.errorMessages=new class{constructor(){this.base="TD:",this.failedToSetInvalidDate="Failed to set invalid date",this.failedToParseInput="Failed parse input field"}unexpectedOption(t){const s=new e(`${this.base} Unexpected option: ${t} does not match a known option.`);throw s.code=1,s}unexpectedOptions(t){const s=new e(`${this.base}: ${t.join(", ")}`);throw s.code=1,s}unexpectedOptionValue(t,s,i){const o=new e(`${this.base} Unexpected option value: ${t} does not accept a value of "${s}". Valid values are: ${i.join(", ")}`);throw o.code=2,o}typeMismatch(t,s,i){const o=new e(`${this.base} Mismatch types: ${t} has a type of ${s} instead of the required ${i}`);throw o.code=3,o}numbersOutOfRange(t,s,i){const o=new e(`${this.base} ${t} expected an array of number between ${s} and ${i}.`);throw o.code=4,o}failedToParseDate(t,s,i=!1){const o=new e(`${this.base} Could not correctly parse "${s}" to a date for ${t}.`);if(o.code=5,!i)throw o;console.warn(o)}mustProvideElement(){const t=new e(`${this.base} No element was provided.`);throw t.code=6,t}subscribeMismatch(){const t=new e(`${this.base} The subscribed events does not match the number of callbacks`);throw t.code=7,t}conflictingConfiguration(t){const s=new e(`${this.base} A configuration value conflicts with another rule. ${t}`);throw s.code=8,s}customDateFormatError(t){const s=new e(`${this.base} Custom Date Format: ${t}`);throw s.code=9,s}dateString(){console.warn(`${this.base} Using a string for date options is not recommended unless you specify an ISO string or use the customDateFormat plugin.`)}deprecatedWarning(t,e){console.warn(`${this.base} Warning ${t} is deprecated and will be removed in a future version. ${e}`)}throwError(t){const s=new e(`${this.base} ${t}`);throw s.code=9,s}};var o,a={...{dateFormats:{LTS:"h:mm:ss T",LT:"h:mm T",L:"MM/dd/yyyy",LL:"MMMM d, yyyy",LLL:"MMMM d, yyyy h:mm T",LLLL:"dddd, MMMM d, yyyy h:mm T"},format:"L LT",locale:"default",hourCycle:void 0,ordinal:t=>{const e=["th","st","nd","rd"],s=t%100;return`[${t}${e[(s-20)%10]||e[s]||e[0]}]`}}};t.Unit=void 0,(o=t.Unit||(t.Unit={})).seconds="seconds",o.minutes="minutes",o.hours="hours",o.date="date",o.month="month",o.year="year";const n={month:"2-digit",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"},r=t=>{switch(t){case"date":return{dateStyle:"short"};case"month":return{month:"numeric",year:"numeric"};case"year":return{year:"numeric"}}},d=t=>{if(!t)return"h12";const e={hour:"2-digit",minute:"2-digit",numberingSystem:"latn"},s=(new l).setLocalization({locale:t});s.hours=0;const i=s.parts(void 0,e).hour;if("12"===i)return"h12";if("24"===i)return"h24";s.hours=23;const o=s.parts(void 0,e).hour;return"00"===i&&"11"===o?"h11":"00"===i&&"23"===o?"h23":void console.warn(`couldn't determine hour cycle for ${t}. start: ${i}. end: ${o}`)};class l extends Date{constructor(){super(...arguments),this.localization=a,this.nonLeapLadder=[0,31,59,90,120,151,181,212,243,273,304,334],this.leapLadder=[0,31,60,91,121,152,182,213,244,274,305,335],this.dateTimeRegex=/(\[[^[\]]*])|y{1,4}|M{1,4}|d{1,4}|H{1,2}|h{1,2}|t|T|m{1,2}|s{1,2}|f{3}/g,this.formattingTokens=/(\[[^[\]]*])|([-_:/.,()\s]+)|(T|t|yyyy|yy?|MM?M?M?|Do|dd?d?d?|hh?|HH?|mm?|ss?)/g,this.match2=/\d\d/,this.match3=/\d{3}/,this.match4=/\d{4}/,this.match1to2=/\d\d?/,this.matchSigned=/[+-]?\d+/,this.matchOffset=/[+-]\d\d:?(\d\d)?|Z/,this.matchWord=/[^\d_:/,\-()\s]+/,this.zoneExpressions=[this.matchOffset,(t,e)=>{t.offset=this.offsetFromString(e)}],this.expressions={t:{pattern:void 0,parser:(t,e)=>{t.afternoon=this.meridiemMatch(e)}},T:{pattern:void 0,parser:(t,e)=>{t.afternoon=this.meridiemMatch(e)}},fff:{pattern:this.match3,parser:(t,e)=>{t.milliseconds=+e}},s:{pattern:this.match1to2,parser:this.addInput("seconds")},ss:{pattern:this.match1to2,parser:this.addInput("seconds")},m:{pattern:this.match1to2,parser:this.addInput("minutes")},mm:{pattern:this.match1to2,parser:this.addInput("minutes")},H:{pattern:this.match1to2,parser:this.addInput("hours")},h:{pattern:this.match1to2,parser:this.addInput("hours")},HH:{pattern:this.match1to2,parser:this.addInput("hours")},hh:{pattern:this.match1to2,parser:this.addInput("hours")},d:{pattern:this.match1to2,parser:this.addInput("day")},dd:{pattern:this.match2,parser:this.addInput("day")},Do:{pattern:this.matchWord,parser:(t,e)=>{if(t.day=+(e.match(/\d+/)[0]||1),this.localization.ordinal)for(let s=1;s<=31;s+=1)this.localization.ordinal(s).replace(/[[\]]/g,"")===e&&(t.day=s)}},M:{pattern:this.match1to2,parser:this.addInput("month")},MM:{pattern:this.match2,parser:this.addInput("month")},MMM:{pattern:this.matchWord,parser:(t,e)=>{const s=this.getAllMonths(),i=(this.getAllMonths("short")||s.map((t=>t.slice(0,3)))).indexOf(e)+1;if(i<1)throw new Error;t.month=i%12||i}},MMMM:{pattern:this.matchWord,parser:(t,e)=>{const s=this.getAllMonths().indexOf(e)+1;if(s<1)throw new Error;t.month=s%12||s}},y:{pattern:this.matchSigned,parser:this.addInput("year")},yy:{pattern:this.match2,parser:(t,e)=>{t.year=this.parseTwoDigitYear(+e)}},yyyy:{pattern:this.match4,parser:this.addInput("year")}}}setLocale(t){return this.localization||(this.localization=a,this.localization.locale=t),this}setLocalization(t){return this.localization=t,this}static convert(t,e="default",s=undefined){if(!t)throw new Error("A date is required");return s||((s=a).locale=e),new l(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()).setLocalization(s)}get clone(){return new l(this.year,this.month,this.date,this.hours,this.minutes,this.seconds,this.getMilliseconds()).setLocalization(this.localization)}static isValid(t){return void 0!==t&&"null"!==JSON.stringify(t)&&t.constructor.name===l.name}startOf(e,s=0){if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);switch(e){case"seconds":this.setMilliseconds(0);break;case"minutes":this.setSeconds(0,0);break;case"hours":this.setMinutes(0,0,0);break;case"date":this.setHours(0,0,0,0);break;case"weekDay":{if(this.startOf(t.Unit.date),this.weekDay===s)break;const e=(this.weekDay-s+7)%7;this.manipulate(-1*e,t.Unit.date);break}case"month":this.startOf(t.Unit.date),this.setDate(1);break;case"year":this.startOf(t.Unit.date),this.setMonth(0,1)}return this}endOf(e,s=0){if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);switch(e){case"seconds":this.setMilliseconds(999);break;case"minutes":this.setSeconds(59,999);break;case"hours":this.setMinutes(59,59,999);break;case"date":this.setHours(23,59,59,999);break;case"weekDay":{this.endOf(t.Unit.date);const e=6+s;if(this.weekDay===e)break;this.manipulate(e-this.weekDay,t.Unit.date);break}case"month":this.endOf(t.Unit.date),this.manipulate(1,t.Unit.month),this.setDate(0);break;case"year":this.endOf(t.Unit.date),this.setMonth(11,31)}return this}manipulate(t,e){if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);return this[e]+=t,this}isBefore(t,e){if(!l.isValid(t))return!1;if(!e)return this.valueOf()<t.valueOf();if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);return this.clone.startOf(e).valueOf()<t.clone.startOf(e).valueOf()}isAfter(t,e){if(!l.isValid(t))return!1;if(!e)return this.valueOf()>t.valueOf();if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);return this.clone.startOf(e).valueOf()>t.clone.startOf(e).valueOf()}isSame(t,e){if(!l.isValid(t))return!1;if(!e)return this.valueOf()===t.valueOf();if(void 0===this[e])throw new Error(`Unit '${e}' is not valid`);return t=l.convert(t),this.clone.startOf(e).valueOf()===t.startOf(e).valueOf()}isBetween(t,e,s,i="()"){if(!l.isValid(t)||!l.isValid(e))return!1;if(s&&void 0===this[s])throw new Error(`Unit '${s}' is not valid`);const o="("===i[0],a=")"===i[1],n=o?this.isAfter(t,s):!this.isBefore(t,s),r=a?this.isBefore(e,s):!this.isAfter(e,s);return n&&r}parts(t=this.localization.locale,e={dateStyle:"full",timeStyle:"long"}){const s={};return new Intl.DateTimeFormat(t,e).formatToParts(this).filter((t=>"literal"!==t.type)).forEach((t=>s[t.type]=t.value)),s}get seconds(){return this.getSeconds()}set seconds(t){this.setSeconds(t)}get secondsFormatted(){return this.parts(void 0,n).second}get minutes(){return this.getMinutes()}set minutes(t){this.setMinutes(t)}get minutesFormatted(){return this.parts(void 0,n).minute}get hours(){return this.getHours()}set hours(t){this.setHours(t)}getHoursFormatted(t="h12"){return this.parts(void 0,{...n,hourCycle:t}).hour}meridiem(t=this.localization.locale){return new Intl.DateTimeFormat(t,{hour:"numeric",hour12:!0}).formatToParts(this).find((t=>"dayPeriod"===t.type))?.value}get date(){return this.getDate()}set date(t){this.setDate(t)}get dateFormatted(){return this.parts(void 0,n).day}get weekDay(){return this.getDay()}get month(){return this.getMonth()}set month(t){const e=new Date(this.year,t+1);e.setDate(0);const s=e.getDate();this.date>s&&(this.date=s),this.setMonth(t)}get monthFormatted(){return this.parts(void 0,n).month}get year(){return this.getFullYear()}set year(t){this.setFullYear(t)}get week(){const t=this.computeOrdinal(),e=this.getUTCDay();let s=Math.floor((t-e+10)/7);return s<1?s=this.weeksInWeekYear():s>this.weeksInWeekYear()&&(s=1),s}weeksInWeekYear(){const t=(this.year+Math.floor(this.year/4)-Math.floor(this.year/100)+Math.floor(this.year/400))%7,e=this.year-1,s=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7;return 4===t||3===s?53:52}get isLeapYear(){return this.year%4==0&&(this.year%100!=0||this.year%400==0)}computeOrdinal(){return this.date+(this.isLeapYear?this.leapLadder:this.nonLeapLadder)[this.month]}getAllMonths(t="long"){const e=new Intl.DateTimeFormat(this.localization.locale,{month:t}).format;return[...Array(12).keys()].map((t=>e(new Date(2021,t))))}replaceTokens(t,e){return t.replace(/(\[[^[\]]*])|(LTS?|l{1,4}|L{1,4})/g,((t,s,i)=>{const o=i&&i.toUpperCase();return s||e[o]||a.dateFormats[o]}))}parseTwoDigitYear(t){return t+(t>68?1900:2e3)}offsetFromString(t){if(!t)return 0;if("Z"===t)return 0;const[e,s,i]=t.match(/([+-]|\d\d)/g),o=60*+s+(+i||0);return 0===o?0:"+"===e?-o:o}zoneInformation(t,e){let s=t.parts(this.localization.locale,{timeZoneName:"longOffset"}).timeZoneName.replace("GMT","").replace(":","");const i=s.includes("-");return s=s.replace("-",""),"z"===e?s=s.substring(1,2):"zz"===e&&(s=s.substring(0,2)),`${i?"-":""}${s}`}addInput(t){return(e,s)=>{e[t]=+s}}getLocaleAfternoon(){return new Intl.DateTimeFormat(this.localization.locale,{hour:"numeric",hour12:!0}).formatToParts(new Date(2022,3,4,13)).find((t=>"dayPeriod"===t.type))?.value?.replace(/\s+/g," ")}meridiemMatch(t){return t.toLowerCase()===this.getLocaleAfternoon().toLowerCase()}correctHours(t){const{afternoon:e}=t;if(void 0!==e){const{hours:s}=t;e?s<12&&(t.hours+=12):12===s&&(t.hours=0),delete t.afternoon}}makeParser(t){const e=(t=this.replaceTokens(t,this.localization.dateFormats)).match(this.formattingTokens),{length:s}=e,i=[];for(let t=0;t<s;t+=1){const s=e[t],o=this.expressions[s];i[t]=o?.parser?o:s.replace(/^\[[^[\]]*]$/g,"")}return t=>{const e={hours:0,minutes:0,seconds:0,milliseconds:0};for(let o=0,a=0;o<s;o+=1){const s=i[o];if("string"==typeof s)a+=s.length;else{const i=t.slice(a);let o=i;if(s.pattern){o=s.pattern.exec(i)[0]}s.parser.call(this,e,o),t=t.replace(o,"")}}return this.correctHours(e),e}}static fromString(t,e){e?.format||i.errorMessages.customDateFormatError("No format was provided");try{const s=new l;if(s.setLocalization(e),["x","X"].indexOf(e.format)>-1)return new l(("X"===e.format?1e3:1)*+t);t=t.replace(/\s+/g," ");const i=s.makeParser(e.format),{year:o,month:a,day:n,hours:r,minutes:d,seconds:c,milliseconds:h,zone:p}=i(t),u=n||(o||a?1:s.getDate()),m=o||s.getFullYear();let y=0;return o&&!a||(y=a>0?a-1:s.getMonth()),p?new l(Date.UTC(m,y,u,r,d,c,h+60*p.offset*1e3)):new l(m,y,u,r,d,c,h)}catch(s){i.errorMessages.customDateFormatError(`Unable to parse provided input: ${t}, format: ${e.format}`)}}format(t,e=this.localization.locale){if(t&&"object"==typeof t)return new Intl.DateTimeFormat(e,t).format(this);const s=this.replaceTokens(t||this.localization.format||`${a.dateFormats.L}, ${a.dateFormats.LT}`,this.localization.dateFormats),i=t=>new Intl.DateTimeFormat(this.localization.locale,t).format(this);this.localization.hourCycle||(this.localization.hourCycle=d(this.localization.locale));const o=this.localization.hourCycle.startsWith("h1")?"h24":this.localization.hourCycle,n=this.localization.hourCycle.startsWith("h2")?"h12":this.localization.hourCycle,r={y:this.year,yy:i({year:"2-digit"}),yyyy:this.year,M:i({month:"numeric"}),MM:this.monthFormatted,MMM:this.getAllMonths("short")[this.getMonth()],MMMM:this.getAllMonths()[this.getMonth()],d:this.date,dd:this.dateFormatted,ddd:i({weekday:"short"}),dddd:i({weekday:"long"}),H:this.getHours(),HH:this.getHoursFormatted(o),h:this.hours>12?this.hours-12:this.hours,hh:this.getHoursFormatted(n),t:this.meridiem(),T:this.meridiem().toUpperCase(),m:this.minutes,mm:this.minutesFormatted,s:this.seconds,ss:this.secondsFormatted,fff:this.getMilliseconds()};return s.replace(this.dateTimeRegex,((t,e)=>e||r[t])).replace(/\[/g,"").replace(/]/g,"")}}class c{constructor(){this.cache=new Map}locate(t){const e=this.cache.get(t);if(e)return e;const s=new t;return this.cache.set(t,s),s}}let h;const p=[{name:"calendar",className:i.css.daysContainer,unit:t.Unit.month,step:1},{name:"months",className:i.css.monthsContainer,unit:t.Unit.year,step:1},{name:"years",className:i.css.yearsContainer,unit:t.Unit.year,step:10},{name:"decades",className:i.css.decadesContainer,unit:t.Unit.year,step:100}];class u{constructor(){this._currentCalendarViewMode=0,this._viewDate=new l,this.minimumCalendarViewMode=0,this.currentView="calendar"}get currentCalendarViewMode(){return this._currentCalendarViewMode}set currentCalendarViewMode(t){this._currentCalendarViewMode=t,this.currentView=p[t].name}get viewDate(){return this._viewDate}set viewDate(t){this._viewDate=t,this.options&&(this.options.viewDate=t)}refreshCurrentView(){this.currentView=p[this.currentCalendarViewMode].name}get isTwelveHour(){return["h12","h11"].includes(this.options.localization.hourCycle)}}class m{constructor(){this.optionsStore=h.locate(u)}isValid(e,s){if(!this._enabledDisabledDatesIsValid(s,e))return!1;if(s!==t.Unit.month&&s!==t.Unit.year&&this.optionsStore.options.restrictions.daysOfWeekDisabled?.length>0&&-1!==this.optionsStore.options.restrictions.daysOfWeekDisabled.indexOf(e.weekDay))return!1;if(!this._minMaxIsValid(s,e))return!1;if(s===t.Unit.hours||s===t.Unit.minutes||s===t.Unit.seconds){if(!this._enabledDisabledHoursIsValid(e))return!1;if(0!==this.optionsStore.options.restrictions.disabledTimeIntervals?.filter((t=>e.isBetween(t.from,t.to))).length)return!1}return!0}_enabledDisabledDatesIsValid(e,s){return e!==t.Unit.date||!(this.optionsStore.options.restrictions.disabledDates.length>0&&this._isInDisabledDates(s))&&!(this.optionsStore.options.restrictions.enabledDates.length>0&&!this._isInEnabledDates(s))}_isInDisabledDates(e){return!(!this.optionsStore.options.restrictions.disabledDates||0===this.optionsStore.options.restrictions.disabledDates.length)&&!!this.optionsStore.options.restrictions.disabledDates.find((s=>s.isSame(e,t.Unit.date)))}_isInEnabledDates(e){return!this.optionsStore.options.restrictions.enabledDates||0===this.optionsStore.options.restrictions.enabledDates.length||!!this.optionsStore.options.restrictions.enabledDates.find((s=>s.isSame(e,t.Unit.date)))}_minMaxIsValid(t,e){return(!this.optionsStore.options.restrictions.minDate||!e.isBefore(this.optionsStore.options.restrictions.minDate,t))&&(!this.optionsStore.options.restrictions.maxDate||!e.isAfter(this.optionsStore.options.restrictions.maxDate,t))}_enabledDisabledHoursIsValid(t){return!(this.optionsStore.options.restrictions.disabledHours.length>0&&this._isInDisabledHours(t))&&!(this.optionsStore.options.restrictions.enabledHours.length>0&&!this._isInEnabledHours(t))}_isInDisabledHours(t){if(!this.optionsStore.options.restrictions.disabledHours||0===this.optionsStore.options.restrictions.disabledHours.length)return!1;const e=t.hours;return this.optionsStore.options.restrictions.disabledHours.includes(e)}_isInEnabledHours(t){if(!this.optionsStore.options.restrictions.enabledHours||0===this.optionsStore.options.restrictions.enabledHours.length)return!0;const e=t.hours;return this.optionsStore.options.restrictions.enabledHours.includes(e)}dateRangeIsValid(e,s,i){if(!this.optionsStore.options.dateRange)return!0;if(2!==e.length&&1!==s)return!0;const o=e[0].clone;if(o.isSame(i,t.Unit.date))return!0;for(o.manipulate(1,t.Unit.date);!o.isSame(i,t.Unit.date);){if(!this.isValid(o,t.Unit.date))return!1;o.manipulate(1,t.Unit.date)}return!0}}class y{constructor(){this.subscribers=[]}subscribe(t){return this.subscribers.push(t),this.unsubscribe.bind(this,this.subscribers.length-1)}unsubscribe(t){this.subscribers.splice(t,1)}emit(t){this.subscribers.forEach((e=>{e(t)}))}destroy(){this.subscribers=null,this.subscribers=[]}}class g{constructor(){this.triggerEvent=new y,this.viewUpdate=new y,this.updateDisplay=new y,this.action=new y,this.updateViewDate=new y}destroy(){this.triggerEvent.destroy(),this.viewUpdate.destroy(),this.updateDisplay.destroy(),this.action.destroy(),this.updateViewDate.destroy()}}const v={clear:"Clear selection",close:"Close the picker",dateFormats:a.dateFormats,dayViewHeaderFormat:{month:"long",year:"2-digit"},decrementHour:"Decrement Hour",decrementMinute:"Decrement Minute",decrementSecond:"Decrement Second",format:a.format,hourCycle:a.hourCycle,incrementHour:"Increment Hour",incrementMinute:"Increment Minute",incrementSecond:"Increment Second",locale:a.locale,maxWeekdayLength:0,nextCentury:"Next Century",nextDecade:"Next Decade",nextMonth:"Next Month",nextYear:"Next Year",ordinal:a.ordinal,pickHour:"Pick Hour",pickMinute:"Pick Minute",pickSecond:"Pick Second",previousCentury:"Previous Century",previousDecade:"Previous Decade",previousMonth:"Previous Month",previousYear:"Previous Year",selectDate:"Select Date",selectDecade:"Select Decade",selectMonth:"Select Month",selectTime:"Select Time",selectYear:"Select Year",startOfTheWeek:0,today:"Go to today",toggleMeridiem:"Toggle Meridiem"},f={allowInputToggle:!1,container:void 0,dateRange:!1,debug:!1,defaultDate:void 0,display:{icons:{type:"icons",time:"fa-solid fa-clock",date:"fa-solid fa-calendar",up:"fa-solid fa-arrow-up",down:"fa-solid fa-arrow-down",previous:"fa-solid fa-chevron-left",next:"fa-solid fa-chevron-right",today:"fa-solid fa-calendar-check",clear:"fa-solid fa-trash",close:"fa-solid fa-xmark"},sideBySide:!1,calendarWeeks:!1,viewMode:"calendar",toolbarPlacement:"bottom",keepOpen:!1,buttons:{today:!1,clear:!1,close:!1},components:{calendar:!0,date:!0,month:!0,year:!0,decades:!0,clock:!0,hours:!0,minutes:!0,seconds:!1,useTwentyfourHour:void 0},inline:!1,theme:"auto",placement:"bottom"},keepInvalid:!1,localization:v,meta:{},multipleDates:!1,multipleDatesSeparator:"; ",promptTimeOnDateChange:!1,promptTimeOnDateChangeTransitionDelay:200,restrictions:{minDate:void 0,maxDate:void 0,disabledDates:[],enabledDates:[],daysOfWeekDisabled:[],disabledTimeIntervals:[],disabledHours:[],enabledHours:[]},stepping:1,useCurrent:!0,viewDate:new l},S={...v};function w(t,e){if(!t)return null;if(t.constructor.name===l.name)return t;if(t.constructor.name===Date.name)return l.convert(t);if("string"==typeof t){const s=l.fromString(t,e);return"null"===JSON.stringify(s)?null:s}return null}function b(t,e,s){"string"==typeof t&&"input"!==e&&i.errorMessages.dateString();const o=w(t,s);return o||i.errorMessages.failedToParseDate(e,t,"input"===e),o}function D(t,e,s,o=a){Array.isArray(e)||i.errorMessages.typeMismatch(t,s,"array of DateTime or Date");for(let s=0;s<e.length;s++){const i=b(e[s],t,o);i.setLocalization(o),e[s]=i}}function _(t,e,s){Array.isArray(e)&&!e.some((t=>"number"!=typeof t))||i.errorMessages.typeMismatch(t,s,"array of numbers")}function k(t){return({value:e,providedType:s,localization:i})=>{const o=b(e,t,i);if(void 0!==o)return o.setLocalization(i),o}}function C(t){const e=k(t);return t=>void 0===t.value?t.value:e(t)}function M(t,e,s){return({value:o,providedType:a})=>void 0===o?[]:(_(t,o,a),o.some((t=>t<e||t>s))&&i.errorMessages.numbersOutOfRange(t,e,s),o)}function E(t){return M(t,0,23)}function T(t){return({value:e,providedType:s,localization:i})=>void 0===e?[]:(D(t,e,s,i),e)}function L(t){return({value:e,path:s})=>(t.includes(e)||i.errorMessages.unexpectedOptionValue(s.substring(1),e,t),e)}const U=Object.freeze({defaultDate:k("defaultDate"),viewDate:k("viewDate"),minDate:C("restrictions.minDate"),maxDate:C("restrictions.maxDate"),disabledHours:E("restrictions.disabledHours"),enabledHours:E("restrictions.enabledHours"),disabledDates:T("restrictions.disabledDates"),enabledDates:T("restrictions.enabledDates"),daysOfWeekDisabled:M("restrictions.daysOfWeekDisabled",0,6),disabledTimeIntervals:({key:t,value:e,providedType:s,localization:o})=>{if(void 0===e)return[];Array.isArray(e)||i.errorMessages.typeMismatch(t,s,"array of { from: DateTime|Date, to: DateTime|Date }");const a=e;for(let e=0;e<a.length;e++)Object.keys(a[e]).forEach((s=>{const i=`${t}[${e}].${s}`,n=b(a[e][s],i,o);n.setLocalization(o),a[e][s]=n}));return a},toolbarPlacement:L(["top","bottom","default"]),type:L(["icons","sprites"]),viewMode:L(["clock","calendar","months","years","decades"]),theme:L(["light","dark","auto"]),placement:L(["top","bottom"]),meta:({value:t})=>t,dayViewHeaderFormat:({value:t})=>t,container:({value:t,path:e})=>(t&&!(t instanceof HTMLElement||t instanceof Element||t?.appendChild)&&i.errorMessages.typeMismatch(e.substring(1),typeof t,"HTMLElement"),t),useTwentyfourHour:({value:t,path:e,providedType:s,defaultType:o})=>{if(i.errorMessages.deprecatedWarning("useTwentyfourHour",'Please use "options.localization.hourCycle" instead'),void 0===t||"boolean"===s)return t;i.errorMessages.typeMismatch(e,s,o)},hourCycle:L(["h11","h12","h23","h24"])}),A=({value:t,defaultType:e,providedType:s,path:o})=>{switch(e){case"boolean":return"true"===t||!0===t;case"number":return+t;case"string":return t.toString();case"object":return{};case"function":return t;default:i.errorMessages.typeMismatch(o,s,e)}};class V{static deepCopy(t){const e={};return Object.keys(t).forEach((s=>{const i=t[s];i instanceof l?e[s]=i.clone:i instanceof Date?e[s]=new Date(i.valueOf()):(e[s]=i,"object"!=typeof i||i instanceof HTMLElement||i instanceof Element||Array.isArray(i)||(e[s]=V.deepCopy(i)))})),e}static objectPath(t,e){return"."===t.charAt(0)&&(t=t.slice(1)),t?t.split(".").reduce(((t,e)=>V.isValue(t)||V.isValue(t[e])?t[e]:void 0),e):e}static spread(t,e,s,o=""){const a=V.objectPath(o,f),n=Object.keys(t).filter((t=>!Object.keys(a).includes(t)));if(n.length>0){const t=V.getFlattenDefaultOptions(),e=n.map((e=>{let s=`"${o}.${e}" in not a known option.`;const i=t.find((t=>t.includes(e)));return i&&(s+=` Did you mean "${i}"?`),s}));i.errorMessages.unexpectedOptions(e)}Object.keys(t).filter((t=>"__proto__"!==t&&"constructor"!==t)).forEach((i=>{"."===(o+=`.${i}`).charAt(0)&&(o=o.slice(1));const n=a[i],r=typeof t[i],d=typeof n,l=t[i];if(null==l)return e[i]=l,void(o=o.substring(0,o.lastIndexOf(`.${i}`)));"object"!=typeof n||Array.isArray(t[i])||n instanceof Date||V.ignoreProperties.includes(i)?e[i]=V.processKey(i,l,r,d,o,s):V.spread(t[i],e[i],s,o),o=o.substring(0,o.lastIndexOf(`.${i}`))}))}static processKey(t,e,s,i,o,a){return(U[(n={key:t,value:e,providedType:s,defaultType:i,path:o,localization:a}).key]||A)(n);var n}static _mergeOptions(t,e){const s=V.deepCopy(e),i="default"!==e.localization?.locale?e.localization:t?.localization||f.localization;return V.spread(t,s,i,""),s}static _dataToOptions(t,e){const s=JSON.parse(JSON.stringify(t.dataset));if(s?.tdTargetInput&&delete s.tdTargetInput,s?.tdTargetToggle&&delete s.tdTargetToggle,!s||0===Object.keys(s).length)return e;const o={},a=t=>{const e={};return Object.keys(t).forEach((t=>{e[t.toLowerCase()]=t})),e},n=this.normalizeObject(a),r=a(e);return Object.keys(s).filter((t=>t.startsWith(i.dataKey))).map((t=>t.substring(2))).forEach((t=>{let i=r[t.toLowerCase()];if(t.includes("_")){const a=t.split("_");i=r[a[0].toLowerCase()],void 0!==i&&e[i].constructor===Object&&(o[i]=n(a,1,e[i],s[`td${t}`]))}else void 0!==i&&(o[i]=s[`td${t}`])})),this._mergeOptions(o,e)}static normalizeObject(t){const e=(s,i,o,a)=>{const n=t(o)[s[i].toLowerCase()],r={};return void 0===n||(o[n]?.constructor===Object?(i++,r[n]=e(s,i,o[n],a)):r[n]=a),r};return e}static _dateTypeCheck(t,e){return w(t,e)}static _typeCheckDateArray(t,e,s,i){return D(t,e,s,i)}static _typeCheckNumberArray(t,e,s){return _(t,e,s)}static dateConversion(t,e,s){return b(t,e,s)}static getFlattenDefaultOptions(){if(this._flattenDefaults)return this._flattenDefaults;const t=(e,s=[])=>Array.isArray(e)?[]:Object(e)===e?Object.entries(e).flatMap((([e,i])=>t(i,[...s,e]))):s.join(".");return this._flattenDefaults=t(f),this._flattenDefaults}static _validateConflicts(t){!t.display.sideBySide||t.display.components.clock&&(t.display.components.hours||t.display.components.minutes||t.display.components.seconds)||i.errorMessages.conflictingConfiguration("Cannot use side by side mode without the clock components"),t.restrictions.minDate&&t.restrictions.maxDate&&(t.restrictions.minDate.isAfter(t.restrictions.maxDate)&&i.errorMessages.conflictingConfiguration("minDate is after maxDate"),t.restrictions.maxDate.isBefore(t.restrictions.minDate)&&i.errorMessages.conflictingConfiguration("maxDate is before minDate")),t.multipleDates&&t.dateRange&&i.errorMessages.conflictingConfiguration('Cannot uss option "multipleDates" with "dateRange"')}}V.ignoreProperties=["meta","dayViewHeaderFormat","container","dateForms","ordinal"],V.isValue=t=>null!=t;class ${constructor(){this._dates=[],this.optionsStore=h.locate(u),this.validation=h.locate(m),this._eventEmitters=h.locate(g)}get picked(){return[...this._dates]}get lastPicked(){return this._dates[this.lastPickedIndex]?.clone}get lastPickedIndex(){return 0===this._dates.length?0:this._dates.length-1}formatInput(t){return t?(t.localization=this.optionsStore.options.localization,t.format()):""}parseInput(t){try{return V.dateConversion(t,"input",this.optionsStore.options.localization)}catch(e){return void this._eventEmitters.triggerEvent.emit({type:i.events.error,reason:i.errorMessages.failedToParseInput,format:this.optionsStore.options.localization.format,value:t})}}setFromInput(t,e){if(!t)return void this.setValue(void 0,e);const s=this.parseInput(t);s&&(s.setLocalization(this.optionsStore.options.localization),this.setValue(s,e))}add(t){this._dates.push(t)}isPicked(t,e){if(!l.isValid(t))return!1;if(!e)return void 0!==this._dates.find((e=>e.isSame(t)));const s=r(e),i=t.format(s);return void 0!==this._dates.map((t=>t.format(s))).find((t=>t===i))}pickedIndex(t,e){if(!l.isValid(t))return-1;if(!e)return this._dates.map((t=>t.valueOf())).indexOf(t.valueOf());const s=r(e),i=t.format(s);return this._dates.map((t=>t.format(s))).indexOf(i)}clear(){this.optionsStore.unset=!0,this._eventEmitters.triggerEvent.emit({type:i.events.change,date:void 0,oldDate:this.lastPicked,isClear:!0,isValid:!0}),this._dates=[],this.optionsStore.input&&(this.optionsStore.input.value=""),this._eventEmitters.updateDisplay.emit("all")}static getStartEndYear(t,e){const s=t/10,i=Math.floor(e/t)*t;return[i,i+9*s,Math.floor(e/s)*s]}updateInput(t){if(!this.optionsStore.input)return;let e=this.formatInput(t);(this.optionsStore.options.multipleDates||this.optionsStore.options.dateRange)&&(e=this._dates.map((t=>this.formatInput(t))).join(this.optionsStore.options.multipleDatesSeparator)),this.optionsStore.input.value!=e&&(this.optionsStore.input.value=e)}setValue(e,s){const o=void 0===s,a=!e&&o;let n=this.optionsStore.unset?null:this._dates[s]?.clone;if(!n&&!this.optionsStore.unset&&o&&a&&(n=this.lastPicked),e&&n?.isSame(e))return void this.updateInput(e);if(!e)return void this._setValueNull(a,s,n);s=s||0,e=e.clone,1!==this.optionsStore.options.stepping&&(e.minutes=Math.round(e.minutes/this.optionsStore.options.stepping)*this.optionsStore.options.stepping,e.startOf(t.Unit.minutes));const r=t=>{this._dates[s]=e,this._eventEmitters.updateViewDate.emit(e.clone),this.updateInput(e),this.optionsStore.unset=!1,this._eventEmitters.updateDisplay.emit("all"),this._eventEmitters.triggerEvent.emit({type:i.events.change,date:e,oldDate:n,isClear:a,isValid:t})};this.validation.isValid(e)&&this.validation.dateRangeIsValid(this.picked,s,e)?r(!0):(this.optionsStore.options.keepInvalid&&r(!1),this._eventEmitters.triggerEvent.emit({type:i.events.error,reason:i.errorMessages.failedToSetInvalidDate,date:e,oldDate:n}))}_setValueNull(t,e,s){!this.optionsStore.options.multipleDates||1===this._dates.length||t?(this.optionsStore.unset=!0,this._dates=[]):this._dates.splice(e,1),this.updateInput(),this._eventEmitters.triggerEvent.emit({type:i.events.change,date:void 0,oldDate:s,isClear:t,isValid:!0}),this._eventEmitters.updateDisplay.emit("all")}}var z;!function(t){t.next="next",t.previous="previous",t.changeCalendarView="changeCalendarView",t.selectMonth="selectMonth",t.selectYear="selectYear",t.selectDecade="selectDecade",t.selectDay="selectDay",t.selectHour="selectHour",t.selectMinute="selectMinute",t.selectSecond="selectSecond",t.incrementHours="incrementHours",t.incrementMinutes="incrementMinutes",t.incrementSeconds="incrementSeconds",t.decrementHours="decrementHours",t.decrementMinutes="decrementMinutes",t.decrementSeconds="decrementSeconds",t.toggleMeridiem="toggleMeridiem",t.togglePicker="togglePicker",t.showClock="showClock",t.showHours="showHours",t.showMinutes="showMinutes",t.showSeconds="showSeconds",t.clear="clear",t.close="close",t.today="today"}(z||(z={}));var O=z;class H{constructor(){this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");if(t.classList.add(i.css.daysContainer),t.append(...this._daysOfTheWeek()),this.optionsStore.options.display.calendarWeeks){const e=document.createElement("div");e.classList.add(i.css.calendarWeeks,i.css.noHighlight),t.appendChild(e)}const{rangeHoverEvent:e,rangeHoverOutEvent:s}=this.handleMouseEvents(t);for(let o=0;o<42;o++){if(0!==o&&o%7==0&&this.optionsStore.options.display.calendarWeeks){const e=document.createElement("div");e.classList.add(i.css.calendarWeeks,i.css.noHighlight),t.appendChild(e)}const a=document.createElement("div");a.setAttribute("data-action",O.selectDay),t.appendChild(a),matchMedia("(hover: hover)").matches&&this.optionsStore.options.dateRange&&(a.addEventListener("mouseover",e),a.addEventListener("mouseout",s))}return t}_update(e,s){const o=e.getElementsByClassName(i.css.daysContainer)[0];this._updateCalendarView(o);const a=this.optionsStore.viewDate.clone.startOf(t.Unit.month).startOf("weekDay",this.optionsStore.options.localization.startOfTheWeek).manipulate(12,t.Unit.hours);this._handleCalendarWeeks(o,a.clone),o.querySelectorAll(`[data-action="${O.selectDay}"]`).forEach((e=>{const o=[];o.push(i.css.day),a.isBefore(this.optionsStore.viewDate,t.Unit.month)&&o.push(i.css.old),a.isAfter(this.optionsStore.viewDate,t.Unit.month)&&o.push(i.css.new),this.optionsStore.unset||this.optionsStore.options.dateRange||!this.dates.isPicked(a,t.Unit.date)||o.push(i.css.active),this.validation.isValid(a,t.Unit.date)||o.push(i.css.disabled),a.isSame(new l,t.Unit.date)&&o.push(i.css.today),0!==a.weekDay&&6!==a.weekDay||o.push(i.css.weekend),this._handleDateRange(a,o),s(t.Unit.date,a,o,e),e.classList.remove(...e.classList),e.classList.add(...o),e.setAttribute("data-value",this._dateToDataValue(a)),e.setAttribute("data-day",`${a.date}`),e.innerText=a.parts(void 0,{day:"numeric"}).day,a.manipulate(1,t.Unit.date)}))}_dateToDataValue(t){return l.isValid(t)?`${t.year}-${t.month.toString().padStart(2,"0")}-${t.date.toString().padStart(2,"0")}`:""}_handleDateRange(e,s){const o=this.dates.picked[0],a=this.dates.picked[1];this.optionsStore.options.dateRange&&(e.isBetween(o,a,t.Unit.date)&&s.push(i.css.rangeIn),e.isSame(o,t.Unit.date)&&s.push(i.css.rangeStart),e.isSame(a,t.Unit.date)&&s.push(i.css.rangeEnd))}handleMouseEvents(e){return{rangeHoverEvent:s=>{const o=s?.currentTarget;if(1!==this.dates.picked.length||o.classList.contains(i.css.disabled))return;const a=[...e.querySelectorAll(".day")],n=o.getAttribute("data-value"),r=l.fromString(n,{format:"yyyy-MM-dd"}),d=a.findIndex((t=>t.getAttribute("data-value")===n)),c=this.dates.picked[0],h=this.dates.picked[1],p=this._dateToDataValue(c),u=a.findIndex((t=>t.getAttribute("data-value")===p)),m=a[u];let y;r.isSame(c,t.Unit.date)||o.classList.remove(i.css.rangeStart),r.isSame(h,t.Unit.date)||o.classList.remove(i.css.rangeEnd),r.isBefore(c)?(o.classList.add(i.css.rangeStart),m?.classList.remove(i.css.rangeStart),m?.classList.add(i.css.rangeEnd),y=(t,e)=>e>d&&e<u):(o.classList.add(i.css.rangeEnd),m?.classList.remove(i.css.rangeEnd),m?.classList.add(i.css.rangeStart),y=(t,e)=>e<d&&e>u),a.filter(y).forEach((t=>{t.classList.add(i.css.rangeIn)}))},rangeHoverOutEvent:s=>{const o=[...e.querySelectorAll(".day")];if(1===this.dates.picked.length&&o.forEach((t=>t.classList.remove(i.css.rangeIn))),1!==this.dates.picked.length)return;const a=s?.currentTarget,n=new l(a.getAttribute("data-value"));n.isSame(this.dates.picked[0],t.Unit.date)||a.classList.remove(i.css.rangeStart),n.isSame(this.dates.picked[1],t.Unit.date)||a.classList.remove(i.css.rangeEnd)}}}_updateCalendarView(e){if("calendar"!==this.optionsStore.currentView)return;const[s,o,a]=e.parentElement.getElementsByClassName(i.css.calendarHeader)[0].getElementsByTagName("div");o.setAttribute(i.css.daysContainer,this.optionsStore.viewDate.format(this.optionsStore.options.localization.dayViewHeaderFormat)),this.optionsStore.options.display.components.month?o.classList.remove(i.css.disabled):o.classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.month),t.Unit.month)?s.classList.remove(i.css.disabled):s.classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(1,t.Unit.month),t.Unit.month)?a.classList.remove(i.css.disabled):a.classList.add(i.css.disabled)}_daysOfTheWeek(){const e=this.optionsStore.viewDate.clone.startOf("weekDay",this.optionsStore.options.localization.startOfTheWeek).startOf(t.Unit.date),s=[];if(document.createElement("div"),this.optionsStore.options.display.calendarWeeks){const t=document.createElement("div");t.classList.add(i.css.calendarWeeks,i.css.noHighlight),t.innerText="#",s.push(t)}for(let o=0;o<7;o++){const o=document.createElement("div");o.classList.add(i.css.dayOfTheWeek,i.css.noHighlight);let a=e.format({weekday:"short"});this.optionsStore.options.localization.maxWeekdayLength>0&&(a=a.substring(0,this.optionsStore.options.localization.maxWeekdayLength)),o.innerText=a,e.manipulate(1,t.Unit.date),s.push(o)}return s}_handleCalendarWeeks(e,s){[...e.querySelectorAll(`.${i.css.calendarWeeks}`)].filter((t=>"#"!==t.innerText)).forEach((e=>{e.innerText=`${s.week}`,s.manipulate(7,t.Unit.date)}))}}class x{constructor(){this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.monthsContainer);for(let e=0;e<12;e++){const e=document.createElement("div");e.setAttribute("data-action",O.selectMonth),t.appendChild(e)}return t}_update(e,s){const o=e.getElementsByClassName(i.css.monthsContainer)[0];if("months"===this.optionsStore.currentView){const[e,s,a]=o.parentElement.getElementsByClassName(i.css.calendarHeader)[0].getElementsByTagName("div");s.setAttribute(i.css.monthsContainer,this.optionsStore.viewDate.format({year:"numeric"})),this.optionsStore.options.display.components.year?s.classList.remove(i.css.disabled):s.classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.year),t.Unit.year)?e.classList.remove(i.css.disabled):e.classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(1,t.Unit.year),t.Unit.year)?a.classList.remove(i.css.disabled):a.classList.add(i.css.disabled)}const a=this.optionsStore.viewDate.clone.startOf(t.Unit.year);o.querySelectorAll(`[data-action="${O.selectMonth}"]`).forEach(((e,o)=>{const n=[];n.push(i.css.month),!this.optionsStore.unset&&this.dates.isPicked(a,t.Unit.month)&&n.push(i.css.active),this.validation.isValid(a,t.Unit.month)||n.push(i.css.disabled),s(t.Unit.month,a,n,e),e.classList.remove(...e.classList),e.classList.add(...n),e.setAttribute("data-value",`${o}`),e.innerText=`${a.format({month:"short"})}`,a.manipulate(1,t.Unit.month)}))}}class I{constructor(){this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.yearsContainer);for(let e=0;e<12;e++){const e=document.createElement("div");e.setAttribute("data-action",O.selectYear),t.appendChild(e)}return t}_update(e,s){this._startYear=this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.year),this._endYear=this.optionsStore.viewDate.clone.manipulate(10,t.Unit.year);const o=e.getElementsByClassName(i.css.yearsContainer)[0];if("years"===this.optionsStore.currentView){const[e,s,a]=o.parentElement.getElementsByClassName(i.css.calendarHeader)[0].getElementsByTagName("div");s.setAttribute(i.css.yearsContainer,`${this._startYear.format({year:"numeric"})}-${this._endYear.format({year:"numeric"})}`),this.optionsStore.options.display.components.decades?s.classList.remove(i.css.disabled):s.classList.add(i.css.disabled),this.validation.isValid(this._startYear,t.Unit.year)?e.classList.remove(i.css.disabled):e.classList.add(i.css.disabled),this.validation.isValid(this._endYear,t.Unit.year)?a.classList.remove(i.css.disabled):a.classList.add(i.css.disabled)}const a=this.optionsStore.viewDate.clone.startOf(t.Unit.year).manipulate(-1,t.Unit.year);o.querySelectorAll(`[data-action="${O.selectYear}"]`).forEach((e=>{const o=[];o.push(i.css.year),!this.optionsStore.unset&&this.dates.isPicked(a,t.Unit.year)&&o.push(i.css.active),this.validation.isValid(a,t.Unit.year)||o.push(i.css.disabled),s(t.Unit.year,a,o,e),e.classList.remove(...e.classList),e.classList.add(...o),e.setAttribute("data-value",`${a.year}`),e.innerText=a.format({year:"numeric"}),a.manipulate(1,t.Unit.year)}))}}class P{constructor(){this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.decadesContainer);for(let e=0;e<12;e++){const e=document.createElement("div");e.setAttribute("data-action",O.selectDecade),t.appendChild(e)}return t}_update(e,s){const[o,a]=$.getStartEndYear(100,this.optionsStore.viewDate.year);this._startDecade=this.optionsStore.viewDate.clone.startOf(t.Unit.year),this._startDecade.year=o,this._endDecade=this.optionsStore.viewDate.clone.startOf(t.Unit.year),this._endDecade.year=a;const n=e.getElementsByClassName(i.css.decadesContainer)[0],[r,d,l]=n.parentElement.getElementsByClassName(i.css.calendarHeader)[0].getElementsByTagName("div");"decades"===this.optionsStore.currentView&&(d.setAttribute(i.css.decadesContainer,`${this._startDecade.format({year:"numeric"})}-${this._endDecade.format({year:"numeric"})}`),this.validation.isValid(this._startDecade,t.Unit.year)?r.classList.remove(i.css.disabled):r.classList.add(i.css.disabled),this.validation.isValid(this._endDecade,t.Unit.year)?l.classList.remove(i.css.disabled):l.classList.add(i.css.disabled));const c=this.dates.picked.map((t=>t.year));n.querySelectorAll(`[data-action="${O.selectDecade}"]`).forEach(((e,o)=>{if(0===o)return e.classList.add(i.css.old),this._startDecade.year-10<0?(e.textContent=" ",r.classList.add(i.css.disabled),e.classList.add(i.css.disabled),void e.setAttribute("data-value","")):(e.innerText=this._startDecade.clone.manipulate(-10,t.Unit.year).format({year:"numeric"}),void e.setAttribute("data-value",`${this._startDecade.year}`));const a=[];a.push(i.css.decade);const n=this._startDecade.year,d=this._startDecade.year+9;!this.optionsStore.unset&&c.filter((t=>t>=n&&t<=d)).length>0&&a.push(i.css.active),this.validation.isValid(this._startDecade,t.Unit.year)||this.validation.isValid(this._startDecade.clone.manipulate(10,t.Unit.year),t.Unit.year)||a.push(i.css.disabled),s("decade",this._startDecade,a,e),e.classList.remove(...e.classList),e.classList.add(...a),e.setAttribute("data-value",`${this._startDecade.year}`),e.innerText=`${this._startDecade.format({year:"numeric"})}`,this._startDecade.manipulate(10,t.Unit.year)}))}}class N{constructor(){this._gridColumns="",this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m)}getPicker(t){const e=document.createElement("div");return e.classList.add(i.css.clockContainer),e.append(...this._grid(t)),e}_update(e){const s=e.getElementsByClassName(i.css.clockContainer)[0];let o=this.dates.lastPicked?.clone;if(!o&&this.optionsStore.options.useCurrent&&(o=this.optionsStore.viewDate.clone),s.querySelectorAll(".disabled").forEach((t=>t.classList.remove(i.css.disabled))),this.optionsStore.options.display.components.hours&&(this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(1,t.Unit.hours),t.Unit.hours)||s.querySelector(`[data-action=${O.incrementHours}]`).classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.hours),t.Unit.hours)||s.querySelector(`[data-action=${O.decrementHours}]`).classList.add(i.css.disabled),s.querySelector(`[data-time-component=${t.Unit.hours}]`).innerText=o?o.getHoursFormatted(this.optionsStore.options.localization.hourCycle):"--"),this.optionsStore.options.display.components.minutes&&(this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(1,t.Unit.minutes),t.Unit.minutes)||s.querySelector(`[data-action=${O.incrementMinutes}]`).classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.minutes),t.Unit.minutes)||s.querySelector(`[data-action=${O.decrementMinutes}]`).classList.add(i.css.disabled),s.querySelector(`[data-time-component=${t.Unit.minutes}]`).innerText=o?o.minutesFormatted:"--"),this.optionsStore.options.display.components.seconds&&(this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(1,t.Unit.seconds),t.Unit.seconds)||s.querySelector(`[data-action=${O.incrementSeconds}]`).classList.add(i.css.disabled),this.validation.isValid(this.optionsStore.viewDate.clone.manipulate(-1,t.Unit.seconds),t.Unit.seconds)||s.querySelector(`[data-action=${O.decrementSeconds}]`).classList.add(i.css.disabled),s.querySelector(`[data-time-component=${t.Unit.seconds}]`).innerText=o?o.secondsFormatted:"--"),this.optionsStore.isTwelveHour){const e=s.querySelector(`[data-action=${O.toggleMeridiem}]`),a=(o||this.optionsStore.viewDate).clone;e.innerText=a.meridiem(),this.validation.isValid(a.manipulate(a.hours>=12?-12:12,t.Unit.hours))?e.classList.remove(i.css.disabled):e.classList.add(i.css.disabled)}s.style.gridTemplateAreas=`"${this._gridColumns}"`}_grid(e){this._gridColumns="";const s=[],o=[],a=[],n=document.createElement("div"),r=e(this.optionsStore.options.display.icons.up),d=e(this.optionsStore.options.display.icons.down);n.classList.add(i.css.separator,i.css.noHighlight);const l=n.cloneNode(!0);l.innerHTML=":";const c=(t=!1)=>t?l.cloneNode(!0):n.cloneNode(!0);if(this.optionsStore.options.display.components.hours){let e=document.createElement("div");e.setAttribute("title",this.optionsStore.options.localization.incrementHour),e.setAttribute("data-action",O.incrementHours),e.appendChild(r.cloneNode(!0)),s.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.pickHour),e.setAttribute("data-action",O.showHours),e.setAttribute("data-time-component",t.Unit.hours),o.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.decrementHour),e.setAttribute("data-action",O.decrementHours),e.appendChild(d.cloneNode(!0)),a.push(e),this._gridColumns+="a"}if(this.optionsStore.options.display.components.minutes){this._gridColumns+=" a",this.optionsStore.options.display.components.hours&&(s.push(c()),o.push(c(!0)),a.push(c()),this._gridColumns+=" a");let e=document.createElement("div");e.setAttribute("title",this.optionsStore.options.localization.incrementMinute),e.setAttribute("data-action",O.incrementMinutes),e.appendChild(r.cloneNode(!0)),s.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.pickMinute),e.setAttribute("data-action",O.showMinutes),e.setAttribute("data-time-component",t.Unit.minutes),o.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.decrementMinute),e.setAttribute("data-action",O.decrementMinutes),e.appendChild(d.cloneNode(!0)),a.push(e)}if(this.optionsStore.options.display.components.seconds){this._gridColumns+=" a",this.optionsStore.options.display.components.minutes&&(s.push(c()),o.push(c(!0)),a.push(c()),this._gridColumns+=" a");let e=document.createElement("div");e.setAttribute("title",this.optionsStore.options.localization.incrementSecond),e.setAttribute("data-action",O.incrementSeconds),e.appendChild(r.cloneNode(!0)),s.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.pickSecond),e.setAttribute("data-action",O.showSeconds),e.setAttribute("data-time-component",t.Unit.seconds),o.push(e),e=document.createElement("div"),e.setAttribute("title",this.optionsStore.options.localization.decrementSecond),e.setAttribute("data-action",O.decrementSeconds),e.appendChild(d.cloneNode(!0)),a.push(e)}if(this.optionsStore.isTwelveHour){this._gridColumns+=" a";let t=c();s.push(t);const e=document.createElement("button");e.setAttribute("type","button"),e.setAttribute("title",this.optionsStore.options.localization.toggleMeridiem),e.setAttribute("data-action",O.toggleMeridiem),e.setAttribute("tabindex","-1"),i.css.toggleMeridiem.includes(",")?e.classList.add(...i.css.toggleMeridiem.split(",")):e.classList.add(i.css.toggleMeridiem),t=document.createElement("div"),t.classList.add(i.css.noHighlight),t.appendChild(e),o.push(t),t=c(),a.push(t)}return this._gridColumns=this._gridColumns.trim(),[...s,...o,...a]}}class F{constructor(){this.optionsStore=h.locate(u),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.hourContainer);for(let e=0;e<(this.optionsStore.isTwelveHour?12:24);e++){const e=document.createElement("div");e.setAttribute("data-action",O.selectHour),t.appendChild(e)}return t}_update(e,s){const o=e.getElementsByClassName(i.css.hourContainer)[0],a=this.optionsStore.viewDate.clone.startOf(t.Unit.date);o.querySelectorAll(`[data-action="${O.selectHour}"]`).forEach((e=>{const o=[];o.push(i.css.hour),this.validation.isValid(a,t.Unit.hours)||o.push(i.css.disabled),s(t.Unit.hours,a,o,e),e.classList.remove(...e.classList),e.classList.add(...o),e.setAttribute("data-value",`${a.hours}`),e.innerText=a.getHoursFormatted(this.optionsStore.options.localization.hourCycle),a.manipulate(1,t.Unit.hours)}))}}class B{constructor(){this.optionsStore=h.locate(u),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.minuteContainer);const e=1===this.optionsStore.options.stepping?5:this.optionsStore.options.stepping;for(let s=0;s<60/e;s++){const e=document.createElement("div");e.setAttribute("data-action",O.selectMinute),t.appendChild(e)}return t}_update(e,s){const o=e.getElementsByClassName(i.css.minuteContainer)[0],a=this.optionsStore.viewDate.clone.startOf(t.Unit.hours),n=1===this.optionsStore.options.stepping?5:this.optionsStore.options.stepping;o.querySelectorAll(`[data-action="${O.selectMinute}"]`).forEach((e=>{const o=[];o.push(i.css.minute),this.validation.isValid(a,t.Unit.minutes)||o.push(i.css.disabled),s(t.Unit.minutes,a,o,e),e.classList.remove(...e.classList),e.classList.add(...o),e.setAttribute("data-value",`${a.minutes}`),e.innerText=a.minutesFormatted,a.manipulate(n,t.Unit.minutes)}))}}class W{constructor(){this.optionsStore=h.locate(u),this.validation=h.locate(m)}getPicker(){const t=document.createElement("div");t.classList.add(i.css.secondContainer);for(let e=0;e<12;e++){const e=document.createElement("div");e.setAttribute("data-action",O.selectSecond),t.appendChild(e)}return t}_update(e,s){const o=e.getElementsByClassName(i.css.secondContainer)[0],a=this.optionsStore.viewDate.clone.startOf(t.Unit.minutes);o.querySelectorAll(`[data-action="${O.selectSecond}"]`).forEach((e=>{const o=[];o.push(i.css.second),this.validation.isValid(a,t.Unit.seconds)||o.push(i.css.disabled),s(t.Unit.seconds,a,o,e),e.classList.remove(...e.classList),e.classList.add(...o),e.setAttribute("data-value",`${a.seconds}`),e.innerText=a.secondsFormatted,a.manipulate(5,t.Unit.seconds)}))}}class q{static toggle(t){t.classList.contains(i.css.show)?this.hide(t):this.show(t)}static showImmediately(t){t.classList.remove(i.css.collapsing),t.classList.add(i.css.collapse,i.css.show),t.style.height=""}static show(t){if(t.classList.contains(i.css.collapsing)||t.classList.contains(i.css.show))return;t.style.height="0",t.classList.remove(i.css.collapse),t.classList.add(i.css.collapsing),setTimeout((()=>{q.showImmediately(t)}),this.getTransitionDurationFromElement(t)),t.style.height=`${t.scrollHeight}px`}static hideImmediately(t){t&&(t.classList.remove(i.css.collapsing,i.css.show),t.classList.add(i.css.collapse))}static hide(t){if(t.classList.contains(i.css.collapsing)||!t.classList.contains(i.css.show))return;t.style.height=`${t.getBoundingClientRect().height}px`;t.offsetHeight,t.classList.remove(i.css.collapse,i.css.show),t.classList.add(i.css.collapsing),t.style.height="",setTimeout((()=>{q.hideImmediately(t)}),this.getTransitionDurationFromElement(t))}}q.getTransitionDurationFromElement=t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:s}=window.getComputedStyle(t);const i=Number.parseFloat(e),o=Number.parseFloat(s);return i||o?(e=e.split(",")[0],s=s.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(s))):0};class Y{constructor(){this._isVisible=!1,this._documentClickEvent=t=>{this.optionsStore.options.debug||window.debug||!this._isVisible||t.composedPath().includes(this.widget)||t.composedPath()?.includes(this.optionsStore.element)||this.hide()},this._actionsClickEvent=t=>{this._eventEmitters.action.emit({e:t})},this.optionsStore=h.locate(u),this.validation=h.locate(m),this.dates=h.locate($),this.dateDisplay=h.locate(H),this.monthDisplay=h.locate(x),this.yearDisplay=h.locate(I),this.decadeDisplay=h.locate(P),this.timeDisplay=h.locate(N),this.hourDisplay=h.locate(F),this.minuteDisplay=h.locate(B),this.secondDisplay=h.locate(W),this._eventEmitters=h.locate(g),this._widget=void 0,this._eventEmitters.updateDisplay.subscribe((t=>{this._update(t)}))}get widget(){return this._widget}get dateContainer(){return this.widget?.querySelector(`div.${i.css.dateContainer}`)}get timeContainer(){return this.widget?.querySelector(`div.${i.css.timeContainer}`)}get isVisible(){return this._isVisible}_update(e){if(this.widget)switch(e){case t.Unit.seconds:this.secondDisplay._update(this.widget,this.paint);break;case t.Unit.minutes:this.minuteDisplay._update(this.widget,this.paint);break;case t.Unit.hours:this.hourDisplay._update(this.widget,this.paint);break;case t.Unit.date:this.dateDisplay._update(this.widget,this.paint);break;case t.Unit.month:this.monthDisplay._update(this.widget,this.paint);break;case t.Unit.year:this.yearDisplay._update(this.widget,this.paint);break;case"decade":this.decadeDisplay._update(this.widget,this.paint);break;case"clock":if(!this._hasTime)break;this.timeDisplay._update(this.widget),this._update(t.Unit.hours),this._update(t.Unit.minutes),this._update(t.Unit.seconds);break;case"calendar":this._update(t.Unit.date),this._update(t.Unit.year),this._update(t.Unit.month),this.decadeDisplay._update(this.widget,this.paint),this._updateCalendarHeader();break;case"all":this._hasTime&&this._update("clock"),this._hasDate&&this._update("calendar")}}paint(t,e,s,i){}show(){if(null==this.widget){if(this._showSetDefaultIfNeeded(),this._buildWidget(),this._updateTheme(),this._showSetupViewMode(),this.optionsStore.options.display.inline)this.optionsStore.element.appendChild(this.widget);else{const t=this.optionsStore.options?.container||document.body,e=this.optionsStore.options?.display?.placement||"bottom";t.appendChild(this.widget),this.createPopup(this.optionsStore.element,this.widget,{modifiers:[{name:"eventListeners",enabled:!0}],placement:"rtl"===document.documentElement.dir?`${e}-end`:`${e}-start`}).then()}"clock"==this.optionsStore.options.display.viewMode&&this._eventEmitters.action.emit({e:null,action:O.showClock}),this.widget.querySelectorAll("[data-action]").forEach((t=>t.addEventListener("click",this._actionsClickEvent))),this._hasTime&&this.optionsStore.options.display.sideBySide&&(this.timeDisplay._update(this.widget),this.widget.getElementsByClassName(i.css.clockContainer)[0].style.display="grid")}this.widget.classList.add(i.css.show),this.optionsStore.options.display.inline||(this.updatePopup(),document.addEventListener("click",this._documentClickEvent)),this._eventEmitters.triggerEvent.emit({type:i.events.show}),this._isVisible=!0}_showSetupViewMode(){const t=this._hasTime&&!this._hasDate;t?(this.optionsStore.currentView="clock",this._eventEmitters.action.emit({e:null,action:O.showClock})):this.optionsStore.currentCalendarViewMode||(this.optionsStore.currentCalendarViewMode=this.optionsStore.minimumCalendarViewMode),t||"clock"===this.optionsStore.options.display.viewMode||(this._hasTime&&(this.optionsStore.options.display.sideBySide?q.show(this.timeContainer):q.hideImmediately(this.timeContainer)),q.show(this.dateContainer)),this._hasDate&&this._showMode()}_showSetDefaultIfNeeded(){if(0==this.dates.picked.length){if(this.optionsStore.options.useCurrent&&!this.optionsStore.options.defaultDate){const e=(new l).setLocalization(this.optionsStore.options.localization);if(!this.optionsStore.options.keepInvalid){let s=0,i=1;for(this.optionsStore.options.restrictions.maxDate?.isBefore(e)&&(i=-1);!this.validation.isValid(e)&&s>31;)e.manipulate(i,t.Unit.date),s++}this.dates.setValue(e)}this.optionsStore.options.defaultDate&&this.dates.setValue(this.optionsStore.options.defaultDate)}}async createPopup(t,e,s){let i;if(window?.Popper)i=window?.Popper?.createPopper;else{const{createPopper:t}=await import("@popperjs/core");i=t}i&&(this._popperInstance=i(t,e,s))}updatePopup(){this._popperInstance?.update()}_showMode(t){if(!this.widget)return;if(t){const e=Math.max(this.optionsStore.minimumCalendarViewMode,Math.min(3,this.optionsStore.currentCalendarViewMode+t));if(this.optionsStore.currentCalendarViewMode==e)return;this.optionsStore.currentCalendarViewMode=e}this.widget.querySelectorAll(`.${i.css.dateContainer} > div:not(.${i.css.calendarHeader}), .${i.css.timeContainer} > div:not(.${i.css.clockContainer})`).forEach((t=>t.style.display="none"));const e=p[this.optionsStore.currentCalendarViewMode],s=this.widget.querySelector(`.${e.className}`);switch(e.className){case i.css.decadesContainer:this.decadeDisplay._update(this.widget,this.paint);break;case i.css.yearsContainer:this.yearDisplay._update(this.widget,this.paint);break;case i.css.monthsContainer:this.monthDisplay._update(this.widget,this.paint);break;case i.css.daysContainer:this.dateDisplay._update(this.widget,this.paint)}s.style.display="grid",this.optionsStore.options.display.sideBySide&&(this.widget.querySelectorAll(`.${i.css.clockContainer}`)[0].style.display="grid"),this._updateCalendarHeader(),this._eventEmitters.viewUpdate.emit()}_updateTheme(t){if(this.widget){if(t){if(this.optionsStore.options.display.theme===t)return;this.optionsStore.options.display.theme=t}this.widget.classList.remove("light","dark"),this.widget.classList.add(this._getThemeClass()),"auto"===this.optionsStore.options.display.theme?window.matchMedia(i.css.isDarkPreferredQuery).addEventListener("change",(()=>this._updateTheme())):window.matchMedia(i.css.isDarkPreferredQuery).removeEventListener("change",(()=>this._updateTheme()))}}_getThemeClass(){const t=this.optionsStore.options.display.theme||"auto",e=window.matchMedia&&window.matchMedia(i.css.isDarkPreferredQuery).matches;switch(t){case"light":return i.css.lightTheme;case"dark":return i.css.darkTheme;case"auto":return e?i.css.darkTheme:i.css.lightTheme}}_updateCalendarHeader(){if(!this._hasDate)return;const t=[...this.widget.querySelector(`.${i.css.dateContainer} div[style*="display: grid"]`).classList].find((t=>t.startsWith(i.css.dateContainer))),[e,s,o]=this.widget.getElementsByClassName(i.css.calendarHeader)[0].getElementsByTagName("div");switch(t){case i.css.decadesContainer:e.setAttribute("title",this.optionsStore.options.localization.previousCentury),s.setAttribute("title",""),o.setAttribute("title",this.optionsStore.options.localization.nextCentury);break;case i.css.yearsContainer:e.setAttribute("title",this.optionsStore.options.localization.previousDecade),s.setAttribute("title",this.optionsStore.options.localization.selectDecade),o.setAttribute("title",this.optionsStore.options.localization.nextDecade);break;case i.css.monthsContainer:e.setAttribute("title",this.optionsStore.options.localization.previousYear),s.setAttribute("title",this.optionsStore.options.localization.selectYear),o.setAttribute("title",this.optionsStore.options.localization.nextYear);break;case i.css.daysContainer:e.setAttribute("title",this.optionsStore.options.localization.previousMonth),s.setAttribute("title",this.optionsStore.options.localization.selectMonth),o.setAttribute("title",this.optionsStore.options.localization.nextMonth),s.setAttribute(t,this.optionsStore.viewDate.format(this.optionsStore.options.localization.dayViewHeaderFormat))}s.innerText=s.getAttribute(t)}hide(){this.widget&&this._isVisible&&(this.widget.classList.remove(i.css.show),this._isVisible&&(this._eventEmitters.triggerEvent.emit({type:i.events.hide,date:this.optionsStore.unset?null:this.dates.lastPicked?.clone}),this._isVisible=!1),document.removeEventListener("click",this._documentClickEvent))}toggle(){return this._isVisible?this.hide():this.show()}_dispose(){document.removeEventListener("click",this._documentClickEvent),this.widget&&(this.widget.querySelectorAll("[data-action]").forEach((t=>t.removeEventListener("click",this._actionsClickEvent))),this.widget.parentNode.removeChild(this.widget),this._widget=void 0)}_buildWidget(){const t=document.createElement("div");t.classList.add(i.css.widget);const e=document.createElement("div");e.classList.add(i.css.dateContainer),e.append(this.getHeadTemplate(),this.decadeDisplay.getPicker(),this.yearDisplay.getPicker(),this.monthDisplay.getPicker(),this.dateDisplay.getPicker());const s=document.createElement("div");s.classList.add(i.css.timeContainer),s.appendChild(this.timeDisplay.getPicker(this._iconTag.bind(this))),s.appendChild(this.hourDisplay.getPicker()),s.appendChild(this.minuteDisplay.getPicker()),s.appendChild(this.secondDisplay.getPicker());const o=document.createElement("div");if(o.classList.add(i.css.toolbar),o.append(...this.getToolbarElements()),this.optionsStore.options.display.inline&&t.classList.add(i.css.inline),this.optionsStore.options.display.calendarWeeks&&t.classList.add("calendarWeeks"),this.optionsStore.options.display.sideBySide&&this._hasDateAndTime)return void this._buildWidgetSideBySide(t,e,s,o);"top"===this.optionsStore.options.display.toolbarPlacement&&t.appendChild(o);const a=(e,s,o,a)=>{e&&(s&&(o.classList.add(i.css.collapse),a&&o.classList.add(i.css.show)),t.appendChild(o))};a(this._hasDate,this._hasTime,e,"clock"!==this.optionsStore.options.display.viewMode),a(this._hasTime,this._hasDate,s,"clock"===this.optionsStore.options.display.viewMode),"bottom"===this.optionsStore.options.display.toolbarPlacement&&t.appendChild(o);const n=document.createElement("div");n.classList.add("arrow"),n.setAttribute("data-popper-arrow",""),t.appendChild(n),this._widget=t}_buildWidgetSideBySide(t,e,s,o){t.classList.add(i.css.sideBySide),"top"===this.optionsStore.options.display.toolbarPlacement&&t.appendChild(o);const a=document.createElement("div");a.classList.add("td-row"),e.classList.add("td-half"),s.classList.add("td-half"),a.appendChild(e),a.appendChild(s),t.appendChild(a),"bottom"===this.optionsStore.options.display.toolbarPlacement&&t.appendChild(o),this._widget=t}get _hasTime(){return this.optionsStore.options.display.components.clock&&(this.optionsStore.options.display.components.hours||this.optionsStore.options.display.components.minutes||this.optionsStore.options.display.components.seconds)}get _hasDate(){return this.optionsStore.options.display.components.calendar&&(this.optionsStore.options.display.components.year||this.optionsStore.options.display.components.month||this.optionsStore.options.display.components.date)}get _hasDateAndTime(){return this._hasDate&&this._hasTime}getToolbarElements(){const t=[];if(this.optionsStore.options.display.buttons.today){const e=document.createElement("div");e.setAttribute("data-action",O.today),e.setAttribute("title",this.optionsStore.options.localization.today),e.appendChild(this._iconTag(this.optionsStore.options.display.icons.today)),t.push(e)}if(!this.optionsStore.options.display.sideBySide&&this._hasDate&&this._hasTime){let e,s;"clock"===this.optionsStore.options.display.viewMode?(e=this.optionsStore.options.localization.selectDate,s=this.optionsStore.options.display.icons.date):(e=this.optionsStore.options.localization.selectTime,s=this.optionsStore.options.display.icons.time);const i=document.createElement("div");i.setAttribute("data-action",O.togglePicker),i.setAttribute("title",e),i.appendChild(this._iconTag(s)),t.push(i)}if(this.optionsStore.options.display.buttons.clear){const e=document.createElement("div");e.setAttribute("data-action",O.clear),e.setAttribute("title",this.optionsStore.options.localization.clear),e.appendChild(this._iconTag(this.optionsStore.options.display.icons.clear)),t.push(e)}if(this.optionsStore.options.display.buttons.close){const e=document.createElement("div");e.setAttribute("data-action",O.close),e.setAttribute("title",this.optionsStore.options.localization.close),e.appendChild(this._iconTag(this.optionsStore.options.display.icons.close)),t.push(e)}return t}getHeadTemplate(){const t=document.createElement("div");t.classList.add(i.css.calendarHeader);const e=document.createElement("div");e.classList.add(i.css.previous),e.setAttribute("data-action",O.previous),e.appendChild(this._iconTag(this.optionsStore.options.display.icons.previous));const s=document.createElement("div");s.classList.add(i.css.switch),s.setAttribute("data-action",O.changeCalendarView);const o=document.createElement("div");return o.classList.add(i.css.next),o.setAttribute("data-action",O.next),o.appendChild(this._iconTag(this.optionsStore.options.display.icons.next)),t.append(e,s,o),t}_iconTag(t){if("sprites"===this.optionsStore.options.display.icons.type){const e=document.createElementNS("http://www.w3.org/2000/svg","svg"),s=document.createElementNS("http://www.w3.org/2000/svg","use");return s.setAttribute("xlink:href",t),s.setAttribute("href",t),e.appendChild(s),e}const e=document.createElement("i");return e.classList.add(...t.split(" ")),e}_rebuild(){const t=this._isVisible;this._dispose(),t&&this.show()}refreshCurrentView(){switch(this._isVisible||this._dispose(),this.optionsStore.currentView){case"clock":this._update("clock");break;case"calendar":this._update(t.Unit.date);break;case"months":this._update(t.Unit.month);break;case"years":this._update(t.Unit.year);break;case"decades":this._update("decade")}}}class j{constructor(){this.optionsStore=h.locate(u),this.dates=h.locate($),this.validation=h.locate(m),this.display=h.locate(Y),this._eventEmitters=h.locate(g),this._eventEmitters.action.subscribe((t=>{this.do(t.e,t.action)}))}do(e,s){const o=e?.currentTarget;if(o?.classList?.contains(i.css.disabled))return;s=s||o?.dataset?.action;const a=(this.dates.lastPicked||this.optionsStore.viewDate).clone;switch(s){case O.next:case O.previous:this.handleNextPrevious(s);break;case O.changeCalendarView:this.display._showMode(1),this.display._updateCalendarHeader();break;case O.selectMonth:case O.selectYear:case O.selectDecade:this.handleSelectCalendarMode(s,o);break;case O.selectDay:this.handleSelectDay(o);break;case O.selectHour:{let t=+o.dataset.value;a.hours>=12&&this.optionsStore.isTwelveHour&&(t+=12),a.hours=t,this.dates.setValue(a,this.dates.lastPickedIndex),this.hideOrClock(e);break}case O.selectMinute:a.minutes=+o.dataset.value,this.dates.setValue(a,this.dates.lastPickedIndex),this.hideOrClock(e);break;case O.selectSecond:a.seconds=+o.dataset.value,this.dates.setValue(a,this.dates.lastPickedIndex),this.hideOrClock(e);break;case O.incrementHours:this.manipulateAndSet(a,t.Unit.hours);break;case O.incrementMinutes:this.manipulateAndSet(a,t.Unit.minutes,this.optionsStore.options.stepping);break;case O.incrementSeconds:this.manipulateAndSet(a,t.Unit.seconds);break;case O.decrementHours:this.manipulateAndSet(a,t.Unit.hours,-1);break;case O.decrementMinutes:this.manipulateAndSet(a,t.Unit.minutes,-1*this.optionsStore.options.stepping);break;case O.decrementSeconds:this.manipulateAndSet(a,t.Unit.seconds,-1);break;case O.toggleMeridiem:this.manipulateAndSet(a,t.Unit.hours,this.dates.lastPicked.hours>=12?-12:12);break;case O.togglePicker:this.handleToggle(o);break;case O.showClock:case O.showHours:case O.showMinutes:case O.showSeconds:this.optionsStore.options.display.sideBySide||"clock"===this.optionsStore.currentView||(q.hideImmediately(this.display.dateContainer),q.showImmediately(this.display.timeContainer)),this.handleShowClockContainers(s);break;case O.clear:this.dates.setValue(null),this.display._updateCalendarHeader();break;case O.close:this.display.hide();break;case O.today:{const e=(new l).setLocalization(this.optionsStore.options.localization);this._eventEmitters.updateViewDate.emit(e),this.validation.isValid(e,t.Unit.date)&&this.dates.setValue(e,this.dates.lastPickedIndex);break}}}handleShowClockContainers(e){if(!this.display._hasTime)return void i.errorMessages.throwError("Cannot show clock containers when time is disabled.");this.optionsStore.currentView="clock",this.display.widget.querySelectorAll(`.${i.css.timeContainer} > div`).forEach((t=>t.style.display="none"));let s="";switch(e){case O.showClock:s=i.css.clockContainer,this.display._update("clock");break;case O.showHours:s=i.css.hourContainer,this.display._update(t.Unit.hours);break;case O.showMinutes:s=i.css.minuteContainer,this.display._update(t.Unit.minutes);break;case O.showSeconds:s=i.css.secondContainer,this.display._update(t.Unit.seconds)}this.display.widget.getElementsByClassName(s)[0].style.display="grid"}handleNextPrevious(t){const{unit:e,step:s}=p[this.optionsStore.currentCalendarViewMode];t===O.next?this.optionsStore.viewDate.manipulate(s,e):this.optionsStore.viewDate.manipulate(-1*s,e),this._eventEmitters.viewUpdate.emit(),this.display._showMode()}hideOrClock(t){this.optionsStore.isTwelveHour||this.optionsStore.options.display.components.minutes||this.optionsStore.options.display.keepOpen||this.optionsStore.options.display.inline?this.do(t,O.showClock):this.display.hide()}manipulateAndSet(t,e,s=1){const i=t.manipulate(s,e);this.validation.isValid(i,e)&&this.dates.setValue(i,this.dates.lastPickedIndex)}handleSelectCalendarMode(t,e){const s=+e.dataset.value;switch(t){case O.selectMonth:this.optionsStore.viewDate.month=s;break;case O.selectYear:case O.selectDecade:this.optionsStore.viewDate.year=s}this.optionsStore.currentCalendarViewMode===this.optionsStore.minimumCalendarViewMode?(this.dates.setValue(this.optionsStore.viewDate,this.dates.lastPickedIndex),this.optionsStore.options.display.inline||this.display.hide()):this.display._showMode(-1)}handleToggle(t){t.getAttribute("title")===this.optionsStore.options.localization.selectDate?(t.setAttribute("title",this.optionsStore.options.localization.selectTime),t.innerHTML=this.display._iconTag(this.optionsStore.options.display.icons.time).outerHTML,this.display._updateCalendarHeader(),this.optionsStore.refreshCurrentView()):(t.setAttribute("title",this.optionsStore.options.localization.selectDate),t.innerHTML=this.display._iconTag(this.optionsStore.options.display.icons.date).outerHTML,this.display._hasTime&&(this.handleShowClockContainers(O.showClock),this.display._update("clock"))),this.display.widget.querySelectorAll(`.${i.css.dateContainer}, .${i.css.timeContainer}`).forEach((t=>q.toggle(t))),this._eventEmitters.viewUpdate.emit()}handleSelectDay(e){const s=this.optionsStore.viewDate.clone;e.classList.contains(i.css.old)&&s.manipulate(-1,t.Unit.month),e.classList.contains(i.css.new)&&s.manipulate(1,t.Unit.month),s.date=+e.dataset.day,this.optionsStore.options.dateRange?this.handleDateRange(s):this.optionsStore.options.multipleDates?this.handleMultiDate(s):this.dates.setValue(s,this.dates.lastPickedIndex),this.display._hasTime||this.optionsStore.options.display.keepOpen||this.optionsStore.options.display.inline||this.optionsStore.options.multipleDates||this.optionsStore.options.dateRange||this.display.hide()}handleMultiDate(e){let s=this.dates.pickedIndex(e,t.Unit.date);console.log(s),-1!==s?this.dates.setValue(null,s):(s=this.dates.lastPickedIndex+1,0===this.dates.picked.length&&(s=0),this.dates.setValue(e,s))}handleDateRange(t){switch(this.dates.picked.length){case 2:this.dates.clear();break;case 1:{const e=this.dates.picked[0];if(t.getTime()===e.getTime()){this.dates.clear();break}return t.isBefore(e)?(this.dates.setValue(t,0),void this.dates.setValue(e,1)):void this.dates.setValue(t,1)}}this.dates.setValue(t,0)}}class R{constructor(t,e={}){this._subscribers={},this._isDisabled=!1,this._inputChangeEvent=t=>{const e=t?.detail;if(e)return;const s=()=>{this.dates.lastPicked&&(this.optionsStore.viewDate=this.dates.lastPicked.clone)},i=this.optionsStore.input.value;if(this.optionsStore.options.multipleDates||this.optionsStore.options.dateRange)try{const t=i.split(this.optionsStore.options.multipleDatesSeparator);for(let e=0;e<t.length;e++)this.dates.setFromInput(t[e],e);s()}catch{console.warn("TD: Something went wrong trying to set the multipleDates values from the input field.")}else this.dates.setFromInput(i,0),s()},this._toggleClickEvent=()=>{this.optionsStore.element?.disabled||this.optionsStore.input?.disabled||"INPUT"===this._toggle.nodeName&&"text"===this._toggle?.type&&this.optionsStore.options.allowInputToggle||this.toggle()},this._openClickEvent=()=>{this.optionsStore.element?.disabled||this.optionsStore.input?.disabled||this.display.isVisible||this.show()},h=new c,this._eventEmitters=h.locate(g),this.optionsStore=h.locate(u),this.display=h.locate(Y),this.dates=h.locate($),this.actions=h.locate(j),t||i.errorMessages.mustProvideElement(),this.optionsStore.element=t,this._initializeOptions(e,f,!0),this.optionsStore.viewDate.setLocalization(this.optionsStore.options.localization),this.optionsStore.unset=!0,this._initializeInput(),this._initializeToggle(),this.optionsStore.options.display.inline&&this.display.show(),this._eventEmitters.triggerEvent.subscribe((t=>{this._triggerEvent(t)})),this._eventEmitters.viewUpdate.subscribe((()=>{this._viewUpdate()})),this._eventEmitters.updateViewDate.subscribe((t=>{this.viewDate=t}))}get viewDate(){return this.optionsStore.viewDate}set viewDate(t){this.optionsStore.viewDate=t,this.optionsStore.viewDate.setLocalization(this.optionsStore.options.localization),this.display._update("clock"===this.optionsStore.currentView?"clock":"calendar")}updateOptions(t,e=!1){e?this._initializeOptions(t,f):this._initializeOptions(t,this.optionsStore.options),this.optionsStore.viewDate.setLocalization(this.optionsStore.options.localization),this.display.refreshCurrentView()}toggle(){this._isDisabled||this.display.toggle()}show(){this._isDisabled||this.display.show()}hide(){this.display.hide()}disable(){this._isDisabled=!0,this.optionsStore.input?.setAttribute("disabled","disabled"),this.display.hide()}enable(){this._isDisabled=!1,this.optionsStore.input?.removeAttribute("disabled")}clear(){this.optionsStore.input.value="",this.dates.clear()}subscribe(t,e){let s;"string"==typeof t&&(t=[t]),s=Array.isArray(e)?e:[e],t.length!==s.length&&i.errorMessages.subscribeMismatch();const o=[];for(let e=0;e<t.length;e++){const i=t[e];if(Array.isArray(this._subscribers[i])||(this._subscribers[i]=[]),this._subscribers[i].push(s[e]),o.push({unsubscribe:this._unsubscribe.bind(this,i,this._subscribers[i].length-1)}),1===t.length)return o[0]}return o}dispose(){this.display.hide(),this.display._dispose(),this._eventEmitters.destroy(),this.optionsStore.input?.removeEventListener("change",this._inputChangeEvent),this.optionsStore.options.allowInputToggle&&(this.optionsStore.input?.removeEventListener("click",this._openClickEvent),this.optionsStore.input?.removeEventListener("focus",this._openClickEvent)),this._toggle?.removeEventListener("click",this._toggleClickEvent),this._subscribers={}}locale(t){const e=Q[t];e&&this.updateOptions({localization:e})}_triggerEvent(t){t.viewMode=this.optionsStore.currentView;const e=t.type===i.events.change;if(e){const{date:e,oldDate:s,isClear:i}=t;if(e&&s&&e.isSame(s)||!i&&!e&&!s)return;this._handleAfterChangeEvent(t),this.optionsStore.input?.dispatchEvent(new CustomEvent("change",{detail:t}))}if(this.optionsStore.element.dispatchEvent(new CustomEvent(t.type,{detail:t})),window.jQuery){const s=window.jQuery;e&&this.optionsStore.input?s(this.optionsStore.input).trigger(t):s(this.optionsStore.element).trigger(t)}this._publish(t)}_publish(t){Array.isArray(this._subscribers[t.type])&&this._subscribers[t.type].forEach((e=>{e(t)}))}_viewUpdate(){this._triggerEvent({type:i.events.update,viewDate:this.optionsStore.viewDate.clone})}_unsubscribe(t,e){this._subscribers[t].splice(e,1)}_initializeOptions(t,e,s=!1){let i=V.deepCopy(t);i=V._mergeOptions(i,e),s&&(i=V._dataToOptions(this.optionsStore.element,i)),V._validateConflicts(i),i.viewDate=i.viewDate.setLocalization(i.localization),this.optionsStore.viewDate.isSame(i.viewDate)||(this.optionsStore.viewDate=i.viewDate),i.display.components.year&&(this.optionsStore.minimumCalendarViewMode=2),i.display.components.month&&(this.optionsStore.minimumCalendarViewMode=1),i.display.components.date&&(this.optionsStore.minimumCalendarViewMode=0),this.optionsStore.currentCalendarViewMode=Math.max(this.optionsStore.minimumCalendarViewMode,this.optionsStore.currentCalendarViewMode),p[this.optionsStore.currentCalendarViewMode].name!==i.display.viewMode&&(this.optionsStore.currentCalendarViewMode=Math.max(p.findIndex((t=>t.name===i.display.viewMode)),this.optionsStore.minimumCalendarViewMode)),this.display?.isVisible&&this.display._update("all"),i.display.components.useTwentyfourHour&&void 0===i.localization.hourCycle?i.localization.hourCycle="h24":void 0===i.localization.hourCycle&&(i.localization.hourCycle=d(i.localization.locale)),this.optionsStore.options=i,i.restrictions.maxDate&&this.viewDate.isAfter(i.restrictions.maxDate)&&(this.viewDate=i.restrictions.maxDate.clone),i.restrictions.minDate&&this.viewDate.isBefore(i.restrictions.minDate)&&(this.viewDate=i.restrictions.minDate.clone)}_initializeInput(){if("INPUT"==this.optionsStore.element.tagName)this.optionsStore.input=this.optionsStore.element;else{const t=this.optionsStore.element.dataset.tdTargetInput;this.optionsStore.input=null==t||"nearest"==t?this.optionsStore.element.querySelector("input"):this.optionsStore.element.querySelector(t)}this.optionsStore.input&&(!this.optionsStore.input.value&&this.optionsStore.options.defaultDate&&(this.optionsStore.input.value=this.dates.formatInput(this.optionsStore.options.defaultDate)),this.optionsStore.input.addEventListener("change",this._inputChangeEvent),this.optionsStore.options.allowInputToggle&&(this.optionsStore.input.addEventListener("click",this._openClickEvent),this.optionsStore.input.addEventListener("focus",this._openClickEvent)),this.optionsStore.input.value&&this._inputChangeEvent())}_initializeToggle(){if(this.optionsStore.options.display.inline)return;let t=this.optionsStore.element.dataset.tdTargetToggle;"nearest"==t&&(t='[data-td-toggle="datetimepicker"]'),this._toggle=null==t?this.optionsStore.element:this.optionsStore.element.querySelector(t),this._toggle.addEventListener("click",this._toggleClickEvent)}_handleAfterChangeEvent(t){!this.optionsStore.options.promptTimeOnDateChange||this.optionsStore.options.multipleDates||this.optionsStore.options.display.inline||this.optionsStore.options.display.sideBySide||!this.display._hasTime||this.display.widget?.getElementsByClassName(i.css.show)[0].classList.contains(i.css.timeContainer)||!t.oldDate&&this.optionsStore.options.useCurrent||t.oldDate&&t.date?.isSame(t.oldDate)||(clearTimeout(this._currentPromptTimeTimeout),this._currentPromptTimeTimeout=setTimeout((()=>{this.display.widget&&this._eventEmitters.action.emit({e:{currentTarget:this.display.widget.querySelector('[data-action="togglePicker"]')},action:O.togglePicker})}),this.optionsStore.options.promptTimeOnDateChangeTransitionDelay))}}const Q={},J=t=>{Q[t.name]||(Q[t.name]=t.localization)},K=t=>{const e=Q[t];e&&(f.localization=e)},Z=function(t,e=undefined){return t?(t.installed||(t(e,{TempusDominus:R,Dates:$,Display:Y,DateTime:l,Namespace:i},X),t.installed=!0),X):X},G="6.9.4",X={TempusDominus:R,extend:Z,loadLocale:J,locale:K,Namespace:i,DefaultOptions:f,DateTime:l,Unit:t.Unit,version:G,DefaultEnLocalization:S};t.DateTime=l,t.DefaultEnLocalization=S,t.DefaultOptions=f,t.Namespace=i,t.TempusDominus=R,t.extend=Z,t.loadLocale=J,t.locale=K,t.version=G,Object.defineProperty(t,"__esModule",{value:!0})}));
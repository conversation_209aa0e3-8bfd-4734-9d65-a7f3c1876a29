#!/usr/bin/env python3
import odoo
from odoo import api, SUPERUSER_ID

# 初始化 Odoo 环境
odoo.tools.config.parse_config(['-c', 'odoo.conf', '-d', 'odoo'])

with api.Environment.manage():
    env = api.Environment(odoo.registry('odoo'), SUPERUSER_ID, {})
    
    # 查找 IZI 相关模块
    izi_modules = env['ir.module.module'].search([('name', 'in', ['izi_data', 'izi_dashboard'])])
    
    print('=== IZI 模块状态 ===')
    for module in izi_modules:
        print(f'模块名: {module.name}')
        print(f'显示名: {module.shortdesc}')
        print(f'状态: {module.state}')
        print(f'可安装: {module.installable}')
        print(f'自动安装: {module.auto_install}')
        print(f'作者: {module.author}')
        print(f'版本: {module.latest_version}')
        print(f'依赖: {[dep.name for dep in module.dependencies_id]}')
        print('---')
    
    if not izi_modules:
        print('未找到 izi_data 或 izi_dashboard 模块')
        # 更新模块列表
        print('正在更新模块列表...')
        env['ir.module.module'].update_list()
        
        # 再次查找
        izi_modules = env['ir.module.module'].search([('name', 'in', ['izi_data', 'izi_dashboard'])])
        if izi_modules:
            print('更新后找到的模块:')
            for module in izi_modules:
                print(f'- {module.name}: {module.state}')
        else:
            # 检查所有包含 izi 的模块
            all_modules = env['ir.module.module'].search([])
            izi_like = [m for m in all_modules if 'izi' in m.name.lower()]
            print(f'找到包含 izi 的模块: {[m.name for m in izi_like]}')

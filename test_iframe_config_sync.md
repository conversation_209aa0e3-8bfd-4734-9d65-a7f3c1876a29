# iframe 组件配置同步问题修复测试

## 问题描述
iframe 组件的 Col（列宽）和 Row（行高）配置修改后不会立即生效，需要手动刷新页面才能看到变化。

## 修复方案
1. **后端同步机制**：已存在完整的同步机制，会正确更新 dashboard block 的 `gs_w` 和 `gs_h` 字段
2. **前端刷新机制**：添加了配置保存后的 dashboard 刷新通知机制

## 修复内容

### 1. 配置保存回调增强 (`izi_config_analysis.js`)
- 在 `_onClickSaveAnalysisVisual` 方法中添加了 iframe 配置检测
- 当检测到 iframeCol 或 iframeRow 配置变更时，调用 `_notifyDashboardRefresh` 方法

### 2. Dashboard 刷新通知机制 (`izi_config_analysis.js`)
- 添加了 `_notifyDashboardRefresh` 方法
- 通过事件系统和全局实例列表通知相关 dashboard 刷新

### 3. Dashboard 事件监听 (`izi_view_dashboard.js`)
- 在 `init` 方法中注册全局实例
- 在 `start` 方法中添加 `iframe_config_updated` 事件监听
- 在 `destroy` 方法中清理全局实例和事件监听器

## 测试步骤

### 前置条件
1. 创建一个包含 iframe 组件的 dashboard
2. 确保 iframe 组件正常显示

### 测试流程
1. **打开配置界面**
   - 进入 Analysis 配置页面
   - 选择 iframe 类型的分析

2. **修改 Col/Row 配置**
   - 修改 Col 值（例如从 12 改为 6）
   - 修改 Row 值（例如从 4 改为 8）
   - 点击保存

3. **验证实时更新**
   - 检查 dashboard 中的 iframe 组件尺寸是否立即更新
   - 验证网格布局是否正确应用新的尺寸
   - 确认不需要手动刷新页面

### 预期结果
- ✅ 配置保存后，iframe 组件尺寸立即更新
- ✅ 网格布局正确应用新的 Col/Row 值
- ✅ 其他组件不受影响
- ✅ 控制台显示刷新通知日志

### 调试信息
- 保存配置时会在控制台显示：`Dashboard refresh notification sent for iframe config update`
- Dashboard 接收事件时会显示：`Received iframe config update event: {analysis_id: xxx}`

## 技术细节

### 事件流程
1. 用户修改 iframe 配置并保存
2. `save_analysis_visual_config` 检测到 Col/Row 配置变更
3. 后端更新 dashboard block 的 `gs_w` 和 `gs_h` 字段
4. 前端检测到 iframe 配置变更，触发 `_notifyDashboardRefresh`
5. 通过事件系统通知所有 dashboard 实例
6. Dashboard 实例接收事件，延迟 500ms 后调用 `_loadFilteredDashboard`
7. 重新加载 dashboard，应用新的网格布局

### 关键代码位置
- **配置检测**：`izi_config_analysis.js` 第 934-947 行
- **刷新通知**：`izi_config_analysis.js` 第 2433-2457 行
- **事件监听**：`izi_view_dashboard.js` 第 109-116 行
- **实例管理**：`izi_view_dashboard.js` 第 68-72 行和第 822-833 行

## 注意事项
1. 延迟刷新（500ms）确保后端数据已完全更新
2. 全局实例管理避免内存泄漏
3. 事件监听器在组件销毁时正确清理
4. 兼容现有的 dashboard 加载机制
